@extends('gym::layouts.app')

@section('page-title', 'Edit Session Type')

@section('content')
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Page Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Session Type</h1>
                    <p class="text-gray-600 mt-2">Update session type information and settings</p>
                </div>
                <a href="{{ route('gym.session-types.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
            <form action="{{ route('gym.session-types.update', $sessionType) }}" method="POST" class="p-8">
                @csrf
                @method('PUT')

                @if ($errors->any())
                    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        <!-- Basic Information -->
                        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-2xl border border-purple-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-info-circle text-sm"></i>
                                </div>
                                Basic Information
                            </h3>

                            <div class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Session Type Name <span class="text-red-500">*</span></label>
                                    <input type="text" name="name" id="name" value="{{ old('name', $sessionType->name) }}" required
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category <span class="text-red-500">*</span></label>
                                    <select name="category" id="category" required
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        <option value="">Select Category</option>
                                        @foreach(\App\Modules\Gym\Models\SessionType::getCategories() as $key => $label)
                                            <option value="{{ $key }}" {{ old('category', $sessionType->category) == $key ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea name="description" id="description" rows="3"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                        placeholder="Describe what this session type includes...">{{ old('description', $sessionType->description) }}</textarea>
                                    @error('description')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Session Details -->
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-2xl border border-blue-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-clock text-sm"></i>
                                </div>
                                Session Details
                            </h3>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                                    <input type="number" name="duration_minutes" id="duration_minutes" value="{{ old('duration_minutes', $sessionType->duration_minutes) }}" min="5" max="480"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('duration_minutes')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="default_price" class="block text-sm font-medium text-gray-700 mb-2">Default Price (Rp)</label>
                                    <input type="number" name="default_price" id="default_price" value="{{ old('default_price', $sessionType->default_price) }}" min="0" step="1000"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('default_price')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Visual Settings -->
                        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-2xl border border-orange-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-yellow-500 to-yellow-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-palette text-sm"></i>
                                </div>
                                Visual Settings
                            </h3>

                            <div class="space-y-4">
                                <div>
                                    <label for="color" class="block text-sm font-medium text-gray-700 mb-2">Color <span class="text-red-500">*</span></label>
                                    <div class="flex items-center space-x-3">
                                        <input type="color" name="color" id="color" value="{{ old('color', $sessionType->color) }}" required
                                            class="w-16 h-10 border border-gray-300 rounded-lg cursor-pointer">
                                        <input type="text" value="{{ old('color', $sessionType->color) }}" readonly
                                            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 bg-gray-50 text-gray-600">
                                    </div>
                                    @error('color')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icon Class <span class="text-red-500">*</span></label>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <i id="icon-preview" class="{{ old('icon', $sessionType->icon) }} text-gray-600"></i>
                                        </div>
                                        <input type="text" name="icon" id="icon" value="{{ old('icon', $sessionType->icon) }}" required
                                            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                            placeholder="e.g., fas fa-dumbbell">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Use FontAwesome icon classes</p>
                                    @error('icon')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl border border-pink-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-cog text-sm"></i>
                                </div>
                                Settings
                            </h3>

                            <div class="space-y-4">
                                <div>
                                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                                    <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $sessionType->sort_order) }}" min="0"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
                                    @error('sort_order')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $sessionType->is_active) ? 'checked' : '' }}
                                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="is_active" class="ml-2 block text-sm text-gray-700">Active</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="is_popular" id="is_popular" value="1" {{ old('is_popular', $sessionType->is_popular) ? 'checked' : '' }}
                                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="is_popular" class="ml-2 block text-sm text-gray-700">Mark as Popular</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <a href="{{ route('gym.session-types.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Update Session Type
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Update color input display
    document.getElementById('color').addEventListener('input', function() {
        document.querySelector('input[readonly]').value = this.value;
    });

    // Update icon preview
    document.getElementById('icon').addEventListener('input', function() {
        const preview = document.getElementById('icon-preview');
        preview.className = this.value + ' text-gray-600';
    });
</script>
@endsection
