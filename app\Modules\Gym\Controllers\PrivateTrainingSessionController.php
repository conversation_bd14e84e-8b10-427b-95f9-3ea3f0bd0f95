<?php

namespace App\Modules\Gym\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gym\Models\PrivateTrainingSession;
use App\Modules\Gym\Models\PrivateTrainingSubscription;
use App\Modules\Gym\Models\Member;
use App\Modules\Gym\Models\Trainer;
use App\Modules\Gym\Models\PrivateTrainingPackage;
use Carbon\Carbon;
use Illuminate\Http\Request;

class PrivateTrainingSessionController extends Controller
{
    /**
     * Display a listing of the private training sessions.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;
        $query = PrivateTrainingSession::where('tenant_id', $tenantId);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->whereHas('member', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('member_code', 'like', "%{$search}%");
                })->orWhereHas('trainer', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
            });
        }

        if ($request->filled('status')) {
            $status = $request->input('status');
            $query->where('status', $status);
        }

        if ($request->filled('trainer_id')) {
            $trainerId = $request->input('trainer_id');
            $query->where('gym_trainer_id', $trainerId);
        }

        if ($request->filled('member_id')) {
            $memberId = $request->input('member_id');
            $query->where('gym_member_id', $memberId);
        }

        if ($request->filled('date_from')) {
            $dateFrom = $request->input('date_from');
            $query->where('scheduled_date', '>=', $dateFrom);
        }

        if ($request->filled('date_to')) {
            $dateTo = $request->input('date_to');
            $query->where('scheduled_date', '<=', $dateTo);
        }

        // Sort
        $sortField = $request->input('sort', 'scheduled_date');
        $sortDirection = $request->input('direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $sessions = $query->with(['member', 'trainer', 'subscription.package'])->paginate(10);

        return view('gym::private-training.index', compact('sessions'));
    }

    /**
     * Show the form for creating a new private training session (standalone).
     *
     * @return \Illuminate\View\View
     */
    public function createSession(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        // Get members, trainers, packages, and rooms for the form
        $members = Member::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $trainers = Trainer::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $packages = PrivateTrainingPackage::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $rooms = \App\Modules\Gym\Models\Room::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->where('is_bookable', true)
            ->orderBy('name')
            ->get();

        return view('gym::private-training.create', compact('members', 'trainers', 'packages', 'rooms'));
    }

    /**
     * Store a newly created private training session (standalone).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSession(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:gym_members,id',
            'trainer_id' => 'required|exists:gym_trainers,id',
            'package_id' => 'required',
            'session_date' => 'required|date|after_or_equal:today',
            'session_time' => 'required',
            'duration' => 'required|integer|min:15',
            'room_id' => 'nullable|exists:gym_rooms,id',
            'location' => 'nullable|string|max:255',
            'session_type' => 'nullable|string',
            'goals' => 'nullable|string',
            'notes' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'payment_status' => 'required|in:pending,paid,partial',
        ]);

        $tenantId = auth()->user()->tenant_id;

        // Create the session
        $sessionData = [
            'tenant_id' => $tenantId,
            'gym_member_id' => $request->member_id,
            'gym_trainer_id' => $request->trainer_id,
            'gym_private_training_subscription_id' => null, // Standalone session
            'gym_private_training_package_id' => $request->package_id,
            'room_id' => $request->room_id,
            'scheduled_date' => $request->session_date,
            'scheduled_start_time' => $request->session_time,
            'scheduled_end_time' => Carbon::parse($request->session_time)->addMinutes((int) $request->duration)->format('H:i:s'),
            'location' => $request->location,
            'session_type' => $request->session_type,
            'goals' => $request->goals,
            'notes' => $request->notes,
            'price' => $request->price,
            'payment_status' => $request->payment_status,
            'status' => 'scheduled',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        $session = PrivateTrainingSession::create($sessionData);

        return redirect()->route('gym.private-training.index')
            ->with('success', 'Private training session scheduled successfully.');
    }

    /**
     * Show the form for creating a new private training session.
     *
     * @param  \App\Modules\Gym\Models\PrivateTrainingSubscription  $subscription
     * @return \Illuminate\View\View
     */
    public function create(PrivateTrainingSubscription $subscription)
    {
        // Check if subscription belongs to the current tenant
        if ($subscription->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if subscription is active
        if (!$subscription->isActive()) {
            return redirect()->route('gym.private-training.subscriptions.show', $subscription)
                ->with('error', 'Cannot create sessions for inactive subscriptions.');
        }

        $subscription->load(['member', 'trainer', 'package']);

        return view('gym::private-training.sessions.create', compact('subscription'));
    }

    /**
     * Store a newly created private training session in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSubscription  $subscription
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, PrivateTrainingSubscription $subscription)
    {
        // Check if subscription belongs to the current tenant
        if ($subscription->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if subscription is active
        if (!$subscription->isActive()) {
            return redirect()->route('gym.private-training.subscriptions.show', $subscription)
                ->with('error', 'Cannot create sessions for inactive subscriptions.');
        }

        $request->validate([
            'scheduled_date' => 'required|date|after_or_equal:today',
            'scheduled_start_time' => 'required',
            'scheduled_end_time' => 'required|after:scheduled_start_time',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $tenantId = auth()->user()->tenant_id;

        $data = $request->all();
        $data['tenant_id'] = $tenantId;
        $data['gym_private_training_subscription_id'] = $subscription->id;
        $data['gym_member_id'] = $subscription->gym_member_id;
        $data['gym_trainer_id'] = $subscription->gym_trainer_id;
        $data['status'] = 'scheduled';
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        $session = PrivateTrainingSession::create($data);

        return redirect()->route('gym.private-training.subscriptions.show', $subscription)
            ->with('success', 'Private training session scheduled successfully.');
    }

    /**
     * Display the specified private training session.
     *
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\View\View
     */
    public function show(PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        $session->load(['member', 'trainer', 'subscription.package']);

        return view('gym::private-training.show', compact('session'));
    }

    /**
     * Show the form for editing the specified private training session.
     *
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\View\View
     */
    public function edit(PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be edited (only scheduled sessions can be edited)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be edited.');
        }

        $session->load(['member', 'trainer', 'subscription.package', 'package']);

        // Get members, trainers, packages, and rooms for the form
        $tenantId = auth()->user()->tenant_id;
        $members = Member::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $trainers = Trainer::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $packages = PrivateTrainingPackage::where('tenant_id', $tenantId)->where('is_active', true)->get();
        $rooms = \App\Modules\Gym\Models\Room::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->where('is_bookable', true)
            ->orderBy('name')
            ->get();

        return view('gym::private-training.edit', compact('session', 'members', 'trainers', 'packages', 'rooms'));
    }

    /**
     * Update the specified private training session in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be edited (only scheduled sessions can be edited)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be edited.');
        }

        $request->validate([
            'gym_member_id' => 'required|exists:gym_members,id',
            'gym_trainer_id' => 'required|exists:gym_trainers,id',
            'package_id' => 'nullable|exists:gym_private_training_packages,id',
            'scheduled_date' => 'required|date',
            'scheduled_start_time' => 'required',
            'scheduled_end_time' => 'required|after:scheduled_start_time',
            'location' => 'nullable|string|max:255',
            'session_type' => 'nullable|string|max:255',
            'goals' => 'nullable|string',
            'notes' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'payment_status' => 'nullable|in:pending,paid,partial',
            'status' => 'nullable|in:scheduled,completed,cancelled,no_show',
        ]);

        $data = $request->all();
        $data['updated_by'] = auth()->id();

        // Map package_id to gym_private_training_package_id
        if (isset($data['package_id'])) {
            $data['gym_private_training_package_id'] = $data['package_id'];
            unset($data['package_id']);
        }

        $session->update($data);

        return redirect()->route('gym.private-training.sessions.show', $session)
            ->with('success', 'Private training session updated successfully.');
    }

    /**
     * Mark the specified private training session as completed.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsCompleted(Request $request, PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be marked as completed (only scheduled sessions can be marked as completed)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be marked as completed.');
        }

        $request->validate([
            'actual_start_time' => 'nullable',
            'actual_end_time' => 'nullable|after:actual_start_time',
            'notes' => 'nullable|string',
        ]);

        $session->markAsCompleted(
            $request->actual_start_time,
            $request->actual_end_time,
            $request->notes
        );

        return redirect()->route('gym.private-training.sessions.show', $session)
            ->with('success', 'Private training session marked as completed.');
    }

    /**
     * Mark the specified private training session as no-show.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsNoShow(Request $request, PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be marked as no-show (only scheduled sessions can be marked as no-show)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be marked as no-show.');
        }

        $request->validate([
            'notes' => 'nullable|string',
        ]);

        $session->markAsNoShow($request->notes);

        return redirect()->route('gym.private-training.sessions.show', $session)
            ->with('success', 'Private training session marked as no-show.');
    }

    /**
     * Mark the specified private training session as cancelled.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsCancelled(Request $request, PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be cancelled (only scheduled sessions can be cancelled)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be cancelled.');
        }

        $request->validate([
            'notes' => 'nullable|string',
            'count_as_missed' => 'boolean',
        ]);

        $countAsMissed = $request->has('count_as_missed');

        $session->markAsCancelled($request->notes, $countAsMissed);

        return redirect()->route('gym.private-training.sessions.show', $session)
            ->with('success', 'Private training session cancelled.');
    }

    /**
     * Show the form for rescheduling the specified private training session.
     *
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\View\View
     */
    public function rescheduleForm(PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be rescheduled (only scheduled sessions can be rescheduled)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be rescheduled.');
        }

        $session->load(['member', 'trainer', 'subscription']);

        return view('gym::private-training.sessions.reschedule', compact('session'));
    }

    /**
     * Reschedule the specified private training session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reschedule(Request $request, PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session can be rescheduled (only scheduled sessions can be rescheduled)
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.sessions.show', $session)
                ->with('error', 'Only scheduled sessions can be rescheduled.');
        }

        $request->validate([
            'new_date' => 'required|date|after_or_equal:today',
            'new_start_time' => 'required',
            'new_end_time' => 'required|after:new_start_time',
            'notes' => 'nullable|string',
        ]);

        $session->reschedule(
            $request->new_date,
            $request->new_start_time,
            $request->new_end_time,
            $request->notes
        );

        return redirect()->route('gym.private-training.subscriptions.show', $session->subscription)
            ->with('success', 'Private training session rescheduled successfully.');
    }

    /**
     * Remove the specified private training session from storage.
     *
     * @param  \App\Modules\Gym\Models\PrivateTrainingSession  $session
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(PrivateTrainingSession $session)
    {
        // Check if session belongs to the current tenant
        if ($session->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow deletion of scheduled sessions
        if ($session->status !== 'scheduled') {
            return redirect()->route('gym.private-training.index')
                ->with('error', 'Only scheduled sessions can be deleted.');
        }

        $session->delete();

        return redirect()->route('gym.private-training.index')
            ->with('success', 'Private training session deleted successfully.');
    }
}
