<?php

namespace App\Modules\Report\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Core\Traits\ModuleServiceProviderTrait;
use Illuminate\Support\Facades\View;

class ReportServiceProvider extends ServiceProvider
{
    use ModuleServiceProviderTrait;

    protected $moduleNamespace = 'App\Modules\Report';
    protected $moduleViewPath = 'report';

    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerModule();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->bootModule();
          // Register views
        $viewPath = __DIR__ . '/../Resources/views';
        $this->loadViewsFrom($viewPath, 'report');
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    /**
     * Register commands for the module
     *
     * @return array
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
