<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Gym Management</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Tailwind CSS CDN (fallback) -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50 overflow-hidden">
    <div class="h-screen flex">
        <!-- Fixed Sidebar -->
        <div class="w-64 bg-white shadow-lg flex flex-col fixed h-full z-30">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 bg-indigo-600 flex-shrink-0">
                <a href="/dashboard" class="flex items-center text-white text-xl font-bold">
                    <img src="{{ asset('images/logo.jpg') }}" alt="House of Pals" class="h-8 w-auto mr-2">
                    Gym Manager
                </a>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 overflow-y-auto custom-scrollbar">
                <div class="px-4 py-4 space-y-2">
                    <!-- Dashboard -->
                    <a href="/gym" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Members Management -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2">
                            Members
                        </div>
                        <a href="/gym/members" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/members*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-users mr-3"></i>
                            All Members
                        </a>
                        <a href="/gym/members/create" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg">
                            <i class="fas fa-user-plus mr-3"></i>
                            Add Member
                        </a>
                        <a href="/gym/memberships" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/memberships*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-id-card mr-3"></i>
                            Membership Plans
                        </a>
                        <a href="/gym/subscriptions" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/subscriptions*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-calendar-check mr-3"></i>
                            Subscriptions
                        </a>
                    </div>

                    <!-- Classes & Training -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2">
                            Classes & Training
                        </div>
                        <a href="/gym/classes" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/classes*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-calendar-alt mr-3"></i>
                            Class Schedule
                        </a>
                        <a href="/gym/trainers" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/trainers*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-user-tie mr-3"></i>
                            Trainers
                        </a>
                        <a href="/gym/private-training" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/private-training') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-user-friends mr-3"></i>
                            Private Training
                        </a>
                        <a href="/gym/private-training/packages" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/private-training/packages*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-box mr-3"></i>
                            PT Packages
                        </a>
                    </div>

                    <!-- Operations -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2">
                            Operations
                        </div>
                        <a href="/gym/attendance" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/attendance*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-clipboard-check mr-3"></i>
                            Attendance
                        </a>
                        <a href="/gym/equipment" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/equipment*') && !request()->is('gym/equipment-categories*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-tools mr-3"></i>
                            Equipment
                        </a>
                        <a href="/gym/equipment-categories" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/equipment-categories*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-layer-group mr-3"></i>
                            Equipment Categories
                        </a>
                        <a href="/gym/rooms" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/rooms*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-door-open mr-3"></i>
                            Room Management
                        </a>
                        <a href="/gym/session-types" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/session-types*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-clock mr-3"></i>
                            Session Types
                        </a>
                        <a href="/gym/staff" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/staff*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-user-friends mr-3"></i>
                            Staff
                        </a>
                    </div>

                    <!-- Products & Sales -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2">
                            Products & Sales
                        </div>
                        <a href="/gym/products" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/products*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-shopping-bag mr-3"></i>
                            Products
                        </a>
                        <a href="/gym/products/categories" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/products/categories*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-tags mr-3"></i>
                            Categories
                        </a>
                    </div>

                    <!-- Reports & Analytics -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2">
                            Reports
                        </div>
                        <a href="/gym/reports" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/reports*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-chart-bar mr-3"></i>
                            Analytics
                        </a>
                        <a href="/gym/private-training/commissions" class="flex items-center px-4 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-lg {{ request()->is('gym/private-training/commissions*') ? 'bg-indigo-50 text-indigo-600' : '' }}">
                            <i class="fas fa-dollar-sign mr-3"></i>
                            PT Commissions
                        </a>
                    </div>
                </div>
            </nav>

            <!-- Back to Main Dashboard -->
            <div class="p-4 border-t border-gray-200 flex-shrink-0">
                <a href="/dashboard" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg border w-full">
                    <i class="fas fa-arrow-left mr-3"></i>
                    Back to Main Dashboard
                </a>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col ml-64">
            <!-- Fixed Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200 fixed top-0 right-0 left-64 z-20">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-800">
                            @yield('page-title', 'Gym Management')
                        </h1>
                        @if(isset($breadcrumbs))
                            <nav class="text-sm text-gray-500">
                                {{ $breadcrumbs }}
                            </nav>
                        @endif
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="text-gray-500 hover:text-gray-700 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                        </button>

                        <!-- User Menu -->
                        <div class="flex items-center space-x-3">
                            @auth
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-700">{{ Auth::user()->name }}</div>
                                    <div class="text-xs text-gray-500">Gym Manager</div>
                                </div>
                                <div class="h-8 w-8 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <form method="POST" action="/logout" class="inline">
                                    @csrf
                                    <button type="submit" class="text-gray-500 hover:text-gray-700">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </button>
                                </form>
                            @endauth
                        </div>
                    </div>
                </div>
            </header>

            <!-- Scrollable Page Content -->
            <main class="flex-1 overflow-y-auto custom-scrollbar pt-16 pb-16">
                <div class="p-6">
                    @yield('content')
                </div>
            </main>

            <!-- Fixed Footer -->
            <footer class="bg-white border-t border-gray-200 fixed bottom-0 right-0 left-64 z-20">
                <div class="px-6 py-3">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div>
                            © {{ date('Y') }} {{ config('app.name', 'Laravel') }}. All rights reserved.
                        </div>
                        <div class="flex items-center space-x-4">
                            <span>Version 1.0.0</span>
                            <span>•</span>
                            <a href="#" class="hover:text-gray-700">Support</a>
                            <span>•</span>
                            <a href="#" class="hover:text-gray-700">Documentation</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Scripts -->
    @stack('scripts')
</body>
</html>
