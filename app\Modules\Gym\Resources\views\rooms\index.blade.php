@extends('gym::layouts.app')

@section('page-title', 'Room Management')

@section('content')
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Page Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Room Management</h1>
                    <p class="text-gray-600 mt-2">Manage gym rooms and training spaces</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('gym.rooms.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        Add Room
                    </a>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                        <i class="fas fa-door-open text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Rooms</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $rooms->total() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Rooms</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $rooms->where('is_active', true)->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                        <i class="fas fa-calendar-check text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Bookable Rooms</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $rooms->where('is_bookable', true)->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Capacity</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $rooms->sum('capacity') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 mb-8">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Rooms</h3>
                <form method="GET" action="{{ route('gym.rooms.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               placeholder="Room name, location..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>

                    <div>
                        <label for="room_type" class="block text-sm font-medium text-gray-700 mb-1">Room Type</label>
                        <select name="room_type" id="room_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">All Types</option>
                            @foreach(\App\Modules\Gym\Models\Room::getRoomTypes() as $key => $label)
                                <option value="{{ $key }}" {{ request('room_type') == $key ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Rooms Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            @forelse($rooms as $room)
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 group">
                    <!-- Room Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center text-white mr-3 
                                {{ $room->is_active ? 'bg-gradient-to-r from-indigo-500 to-indigo-600' : 'bg-gradient-to-r from-gray-400 to-gray-500' }}">
                                <i class="fas fa-door-open text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">{{ $room->name }}</h3>
                                <p class="text-sm text-gray-500">{{ \App\Modules\Gym\Models\Room::getRoomTypes()[$room->room_type] ?? $room->room_type }}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            @if($room->is_active)
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            @else
                                <span class="px-3 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                                    <i class="fas fa-times-circle mr-1"></i>Inactive
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Room Details -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-users w-4 mr-2"></i>
                            <span>Capacity: {{ $room->capacity }} people</span>
                        </div>
                        @if($room->location)
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2"></i>
                                <span>{{ $room->location }}</span>
                            </div>
                        @endif
                        @if($room->hourly_rate)
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-dollar-sign w-4 mr-2"></i>
                                <span>${{ number_format($room->hourly_rate, 2) }}/hour</span>
                            </div>
                        @endif
                        @if($room->is_bookable)
                            <div class="flex items-center text-sm text-green-600">
                                <i class="fas fa-calendar-check w-4 mr-2"></i>
                                <span>Bookable</span>
                            </div>
                        @endif
                    </div>

                    <!-- Room Description -->
                    @if($room->description)
                        <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($room->description, 100) }}</p>
                    @else
                        <p class="text-gray-400 text-sm mb-4 italic">No description</p>
                    @endif

                    <!-- Room Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex space-x-2">
                            <a href="{{ route('gym.rooms.show', $room) }}" class="text-indigo-600 hover:text-indigo-800 p-2 hover:bg-indigo-50 rounded-lg transition-all duration-200" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ route('gym.rooms.edit', $room) }}" class="text-blue-600 hover:text-blue-800 p-2 hover:bg-blue-50 rounded-lg transition-all duration-200" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('gym.rooms.destroy', $room) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded-lg transition-all duration-200" 
                                        onclick="return confirm('Are you sure you want to delete this room?')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                        <div class="text-xs text-gray-500">
                            #{{ $room->id }}
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-6">
                            <i class="fas fa-door-open text-4xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">No Rooms Found</h3>
                        <p class="text-gray-600 mb-6">Get started by creating your first room for training sessions and classes.</p>
                        <a href="{{ route('gym.rooms.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Create First Room
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($rooms->hasPages())
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 px-6 py-4">
                {{ $rooms->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
