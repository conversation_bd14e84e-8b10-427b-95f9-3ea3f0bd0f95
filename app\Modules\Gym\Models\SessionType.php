<?php

namespace App\Modules\Gym\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Tenant;

class SessionType extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'gym_session_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'slug',
        'description',
        'category', // fitness, nutrition, wellness, rehabilitation, assessment, specialized
        'duration_minutes',
        'default_price',
        'color',
        'icon',
        'features', // JSON array of features included
        'requirements', // JSON array of requirements/prerequisites
        'benefits', // JSON array of benefits
        'target_audience', // JSON array of target audience
        'equipment_needed', // JSON array of equipment
        'preparation_notes',
        'post_session_notes',
        'is_active',
        'is_popular',
        'sort_order',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'duration_minutes' => 'integer',
        'default_price' => 'decimal:2',
        'features' => 'array',
        'requirements' => 'array',
        'benefits' => 'array',
        'target_audience' => 'array',
        'equipment_needed' => 'array',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the tenant that owns the session type.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the private training sessions for the session type.
     */
    public function privateTrainingSessions(): HasMany
    {
        return $this->hasMany(PrivateTrainingSession::class, 'gym_session_type_id');
    }

    /**
     * Get the classes for the session type.
     */
    public function classes(): HasMany
    {
        return $this->hasMany(GymClass::class, 'gym_session_type_id');
    }

    /**
     * Scope a query to only include active session types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'N/A';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }

        return $minutes . 'm';
    }

    /**
     * Get the sessions count for the session type.
     */
    public function getSessionsCountAttribute(): int
    {
        return $this->privateTrainingSessions()->count() + $this->classes()->count();
    }

    /**
     * Scope to filter popular session types.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the category display name.
     */
    public function getCategoryDisplayAttribute(): string
    {
        return match($this->category) {
            'fitness' => 'Fitness Training',
            'nutrition' => 'Nutrition & Diet',
            'wellness' => 'Wellness & Recovery',
            'rehabilitation' => 'Rehabilitation',
            'assessment' => 'Assessment & Testing',
            'specialized' => 'Specialized Training',
            default => ucfirst($this->category ?? 'General')
        };
    }

    /**
     * Get available categories.
     */
    public static function getCategories(): array
    {
        return [
            'fitness' => 'Fitness Training',
            'nutrition' => 'Nutrition & Diet',
            'wellness' => 'Wellness & Recovery',
            'rehabilitation' => 'Rehabilitation',
            'assessment' => 'Assessment & Testing',
            'specialized' => 'Specialized Training',
        ];
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if (!$this->default_price) {
            return 'Contact for pricing';
        }
        return 'Rp ' . number_format($this->default_price, 0, ',', '.');
    }
}
