@extends('gym::layouts.app')

@section('page-title', 'Create Session Type')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">Create Session Type</h2>
            <p class="text-gray-600">Add a new training session type</p>
        </div>
        <a href="{{ route('gym.session-types.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Session Types
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-800 flex items-center">
                <i class="fas fa-clock mr-2 text-indigo-600"></i>
                Session Type Information
            </h3>
        </div>

        <form action="{{ route('gym.session-types.store') }}" method="POST" class="p-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Session Type Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="e.g., Strength Training">
                    @error('name')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select name="category" id="category" required
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Select Category</option>
                        @foreach(\App\Modules\Gym\Models\SessionType::getCategories() as $key => $label)
                            <option value="{{ $key }}" {{ old('category') == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('category')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Duration -->
                <div>
                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                        Duration (minutes)
                    </label>
                    <input type="number" name="duration_minutes" id="duration_minutes" value="{{ old('duration_minutes', 60) }}" min="1" max="480"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="60">
                    @error('duration_minutes')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Default Price -->
                <div>
                    <label for="default_price" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Price (Rp)
                    </label>
                    <input type="number" name="default_price" id="default_price" value="{{ old('default_price') }}" min="0" step="1000"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="150000">
                    @error('default_price')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Color -->
                <div>
                    <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                        Color <span class="text-red-500">*</span>
                    </label>
                    <div class="flex items-center space-x-3">
                        <input type="color" name="color" id="color" value="{{ old('color', '#10b981') }}" required
                            class="w-16 h-10 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="color_text" value="{{ old('color', '#10b981') }}" readonly
                            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 bg-gray-50 text-gray-700">
                    </div>
                    @error('color')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        Sort Order
                    </label>
                    <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="0">
                    <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
                    @error('sort_order')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Icon -->
            <div class="mt-6">
                <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                    Icon <span class="text-red-500">*</span>
                </label>
                <div class="flex items-center space-x-3">
                    <div id="icon_preview" class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600">
                        <i class="fas fa-clock"></i>
                    </div>
                    <select name="icon" id="icon" required
                        class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="fas fa-clock" {{ old('icon') == 'fas fa-clock' ? 'selected' : '' }}>Clock</option>
                        <option value="fas fa-user" {{ old('icon') == 'fas fa-user' ? 'selected' : '' }}>Personal</option>
                        <option value="fas fa-dumbbell" {{ old('icon') == 'fas fa-dumbbell' ? 'selected' : '' }}>Strength</option>
                        <option value="fas fa-heartbeat" {{ old('icon') == 'fas fa-heartbeat' ? 'selected' : '' }}>Cardio</option>
                        <option value="fas fa-fire" {{ old('icon') == 'fas fa-fire' ? 'selected' : '' }}>HIIT</option>
                        <option value="fas fa-running" {{ old('icon') == 'fas fa-running' ? 'selected' : '' }}>Functional</option>
                        <option value="fas fa-spa" {{ old('icon') == 'fas fa-spa' ? 'selected' : '' }}>Flexibility</option>
                        <option value="fas fa-weight" {{ old('icon') == 'fas fa-weight' ? 'selected' : '' }}>Weight Loss</option>
                        <option value="fas fa-medal" {{ old('icon') == 'fas fa-medal' ? 'selected' : '' }}>Athletic</option>
                        <option value="fas fa-medkit" {{ old('icon') == 'fas fa-medkit' ? 'selected' : '' }}>Rehabilitation</option>
                        <option value="fas fa-clipboard-list" {{ old('icon') == 'fas fa-clipboard-list' ? 'selected' : '' }}>Consultation</option>
                        <option value="fas fa-users" {{ old('icon') == 'fas fa-users' ? 'selected' : '' }}>Group</option>
                    </select>
                </div>
                @error('icon')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea name="description" id="description" rows="4"
                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Describe this session type...">{{ old('description') }}</textarea>
                @error('description')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Preview -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div id="preview_icon" class="w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl" style="background-color: #10b981">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="ml-3">
                            <h3 id="preview_name" class="text-lg font-semibold text-gray-900">Session Type Name</h3>
                            <p id="preview_duration" class="text-sm text-gray-500">60m</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                <a href="{{ route('gym.session-types.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-save mr-2"></i>Create Session Type
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Update color text input when color picker changes
document.getElementById('color').addEventListener('change', function() {
    document.getElementById('color_text').value = this.value;
    updatePreview();
});

// Update icon preview when icon selection changes
document.getElementById('icon').addEventListener('change', function() {
    document.getElementById('icon_preview').innerHTML = '<i class="' + this.value + '"></i>';
    updatePreview();
});

// Update name preview when name input changes
document.getElementById('name').addEventListener('input', function() {
    updatePreview();
});

// Update duration preview when duration input changes
document.getElementById('duration_minutes').addEventListener('input', function() {
    updatePreview();
});

// Update preview function
function updatePreview() {
    const name = document.getElementById('name').value || 'Session Type Name';
    const color = document.getElementById('color').value;
    const icon = document.getElementById('icon').value;
    const duration = document.getElementById('duration_minutes').value;

    document.getElementById('preview_name').textContent = name;
    document.getElementById('preview_icon').style.backgroundColor = color;
    document.getElementById('preview_icon').innerHTML = '<i class="' + icon + '"></i>';

    if (duration) {
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        let durationText = '';
        if (hours > 0) {
            durationText = hours + 'h';
            if (minutes > 0) {
                durationText += ' ' + minutes + 'm';
            }
        } else {
            durationText = minutes + 'm';
        }
        document.getElementById('preview_duration').textContent = durationText;
    } else {
        document.getElementById('preview_duration').textContent = 'Custom duration';
    }
}

// Initialize preview
updatePreview();
</script>
@endpush
@endsection
