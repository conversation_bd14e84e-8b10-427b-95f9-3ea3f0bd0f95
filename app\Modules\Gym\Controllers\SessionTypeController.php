<?php

namespace App\Modules\Gym\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gym\Models\SessionType;
use Illuminate\Http\Request;

class SessionTypeController extends Controller
{
    /**
     * Display a listing of the session types.
     */
    public function index()
    {
        $tenantId = auth()->user()->tenant_id;

        $sessionTypes = SessionType::where('tenant_id', $tenantId)
            ->withCount(['privateTrainingSessions', 'classes'])
            ->ordered()
            ->paginate(15);

        return view('gym::session-types.index', compact('sessionTypes'));
    }

    /**
     * Show the form for creating a new session type.
     */
    public function create()
    {
        return view('gym::session-types.create');
    }

    /**
     * Store a newly created session type in storage.
     */
    public function store(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        $request->validate([
            'name' => 'required|string|max:255|unique:gym_session_types,name,NULL,id,tenant_id,' . $tenantId,
            'description' => 'nullable|string',
            'category' => 'required|string|in:fitness,nutrition,wellness,rehabilitation,assessment,specialized',
            'duration_minutes' => 'nullable|integer|min:1|max:480', // Max 8 hours
            'default_price' => 'nullable|numeric|min:0',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'required|string|max:255',
            'features' => 'nullable|array',
            'benefits' => 'nullable|array',
            'target_audience' => 'nullable|array',
            'equipment_needed' => 'nullable|array',
            'requirements' => 'nullable|array',
            'preparation_notes' => 'nullable|string',
            'post_session_notes' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_popular' => 'boolean',
        ]);

        $slug = \Illuminate\Support\Str::slug($request->name);
        $originalSlug = $slug;
        $counter = 1;
        while (SessionType::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        SessionType::create([
            'tenant_id' => $tenantId,
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'category' => $request->category,
            'duration_minutes' => $request->duration_minutes,
            'default_price' => $request->default_price,
            'color' => $request->color,
            'icon' => $request->icon,
            'features' => $request->features,
            'benefits' => $request->benefits,
            'target_audience' => $request->target_audience,
            'equipment_needed' => $request->equipment_needed,
            'requirements' => $request->requirements,
            'preparation_notes' => $request->preparation_notes,
            'post_session_notes' => $request->post_session_notes,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => true,
            'is_popular' => $request->has('is_popular'),
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);

        return redirect()->route('gym.session-types.index')
            ->with('success', 'Session type created successfully.');
    }

    /**
     * Display the specified session type.
     */
    public function show(SessionType $sessionType)
    {
        // Check if session type belongs to the current tenant
        if ($sessionType->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        $sessionType->load(['privateTrainingSessions', 'classes']);

        return view('gym::session-types.show', compact('sessionType'));
    }

    /**
     * Show the form for editing the specified session type.
     */
    public function edit(SessionType $sessionType)
    {
        // Check if session type belongs to the current tenant
        if ($sessionType->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        return view('gym::session-types.edit', compact('sessionType'));
    }

    /**
     * Update the specified session type in storage.
     */
    public function update(Request $request, SessionType $sessionType)
    {
        // Check if session type belongs to the current tenant
        if ($sessionType->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        $tenantId = auth()->user()->tenant_id;

        $request->validate([
            'name' => 'required|string|max:255|unique:gym_session_types,name,' . $sessionType->id . ',id,tenant_id,' . $tenantId,
            'description' => 'nullable|string',
            'category' => 'required|string|in:fitness,nutrition,wellness,rehabilitation,assessment,specialized',
            'duration_minutes' => 'nullable|integer|min:1|max:480', // Max 8 hours
            'default_price' => 'nullable|numeric|min:0',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'required|string|max:255',
            'features' => 'nullable|array',
            'benefits' => 'nullable|array',
            'target_audience' => 'nullable|array',
            'equipment_needed' => 'nullable|array',
            'requirements' => 'nullable|array',
            'preparation_notes' => 'nullable|string',
            'post_session_notes' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_popular' => 'boolean',
        ]);

        // Update slug if name changed
        $updateData = [
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'duration_minutes' => $request->duration_minutes,
            'default_price' => $request->default_price,
            'color' => $request->color,
            'icon' => $request->icon,
            'features' => $request->features,
            'benefits' => $request->benefits,
            'target_audience' => $request->target_audience,
            'equipment_needed' => $request->equipment_needed,
            'requirements' => $request->requirements,
            'preparation_notes' => $request->preparation_notes,
            'post_session_notes' => $request->post_session_notes,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->has('is_active'),
            'is_popular' => $request->has('is_popular'),
            'updated_by' => auth()->id(),
        ];

        if ($request->name !== $sessionType->name) {
            $slug = \Illuminate\Support\Str::slug($request->name);
            $originalSlug = $slug;
            $counter = 1;
            while (SessionType::where('slug', $slug)->where('id', '!=', $sessionType->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $updateData['slug'] = $slug;
        }

        $sessionType->update($updateData);

        return redirect()->route('gym.session-types.index')
            ->with('success', 'Session type updated successfully.');
    }

    /**
     * Remove the specified session type from storage.
     */
    public function destroy(SessionType $sessionType)
    {
        // Check if session type belongs to the current tenant
        if ($sessionType->tenant_id !== auth()->user()->tenant_id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if session type is being used
        $sessionsCount = $sessionType->privateTrainingSessions()->count() + $sessionType->classes()->count();
        if ($sessionsCount > 0) {
            return redirect()->route('gym.session-types.index')
                ->with('error', 'Cannot delete session type that is being used in sessions or classes.');
        }

        $sessionType->delete();

        return redirect()->route('gym.session-types.index')
            ->with('success', 'Session type deleted successfully.');
    }
}
