<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if gym_members table exists and members table exists
        if (Schema::hasTable('gym_members') && Schema::hasTable('members')) {
            
            // Migrate gym members to universal members table
            $gymMembers = DB::table('gym_members')->get();
            
            foreach ($gymMembers as $gymMember) {
                // Check if member already exists in universal table
                $existingMember = DB::table('members')
                    ->where('tenant_id', $gymMember->tenant_id)
                    ->where('email', $gymMember->email)
                    ->first();
                
                if (!$existingMember) {
                    DB::table('members')->insert([
                        'id' => $gymMember->id, // Keep same ID for easier migration
                        'tenant_id' => $gymMember->tenant_id,
                        'member_code' => $gymMember->member_code,
                        'name' => $gymMember->name,
                        'email' => $gymMember->email,
                        'password' => $gymMember->password,
                        'email_verified_at' => $gymMember->email_verified_at,
                        'remember_token' => $gymMember->remember_token,
                        'last_login_at' => $gymMember->last_login_at,
                        'phone' => $gymMember->phone,
                        'date_of_birth' => $gymMember->date_of_birth,
                        'gender' => $gymMember->gender,
                        'address' => $gymMember->address,
                        'emergency_contact_name' => $gymMember->emergency_contact_name,
                        'emergency_contact_phone' => $gymMember->emergency_contact_phone,
                        'health_conditions' => $gymMember->health_conditions,
                        'notes' => $gymMember->notes,
                        'photo' => $gymMember->photo,
                        'qr_code' => $gymMember->qr_code,
                        'member_type' => 'gym',
                        'modules' => json_encode(['gym']), // Give gym access
                        'is_active' => $gymMember->is_active,
                        'created_at' => $gymMember->created_at,
                        'updated_at' => $gymMember->updated_at,
                        'deleted_at' => $gymMember->deleted_at,
                    ]);
                }
            }

            // Migrate gym subscriptions to universal member subscriptions
            if (Schema::hasTable('gym_subscriptions') && Schema::hasTable('member_subscriptions')) {
                $gymSubscriptions = DB::table('gym_subscriptions')->get();
                
                foreach ($gymSubscriptions as $gymSubscription) {
                    // Check if subscription already exists
                    $existingSubscription = DB::table('member_subscriptions')
                        ->where('tenant_id', $gymSubscription->tenant_id)
                        ->where('member_id', $gymSubscription->gym_member_id)
                        ->where('subscribable_type', 'App\\Modules\\Gym\\Models\\Membership')
                        ->where('subscribable_id', $gymSubscription->gym_membership_id)
                        ->first();
                    
                    if (!$existingSubscription) {
                        DB::table('member_subscriptions')->insert([
                            'tenant_id' => $gymSubscription->tenant_id,
                            'member_id' => $gymSubscription->gym_member_id,
                            'subscribable_type' => 'App\\Modules\\Gym\\Models\\Membership',
                            'subscribable_id' => $gymSubscription->gym_membership_id,
                            'module_type' => 'gym',
                            'subscription_code' => $gymSubscription->subscription_code ?? 'GYM' . str_pad($gymSubscription->id, 6, '0', STR_PAD_LEFT),
                            'start_date' => $gymSubscription->start_date,
                            'end_date' => $gymSubscription->end_date,
                            'status' => $gymSubscription->status,
                            'price' => $gymSubscription->price,
                            'payment_method' => $gymSubscription->payment_method,
                            'payment_status' => $gymSubscription->payment_status,
                            'auto_renewal' => $gymSubscription->auto_renewal ?? false,
                            'renewal_date' => $gymSubscription->renewal_date,
                            'notes' => $gymSubscription->notes,
                            'created_by' => $gymSubscription->created_by,
                            'updated_by' => $gymSubscription->updated_by,
                            'created_at' => $gymSubscription->created_at,
                            'updated_at' => $gymSubscription->updated_at,
                        ]);
                    }
                }
            }

            // Migrate gym attendances to universal member attendances
            if (Schema::hasTable('gym_attendances') && Schema::hasTable('member_attendances')) {
                $gymAttendances = DB::table('gym_attendances')->get();
                
                foreach ($gymAttendances as $gymAttendance) {
                    // Check if attendance already exists
                    $existingAttendance = DB::table('member_attendances')
                        ->where('tenant_id', $gymAttendance->tenant_id)
                        ->where('member_id', $gymAttendance->gym_member_id)
                        ->where('check_in_time', $gymAttendance->check_in_time)
                        ->first();
                    
                    if (!$existingAttendance) {
                        DB::table('member_attendances')->insert([
                            'tenant_id' => $gymAttendance->tenant_id,
                            'member_id' => $gymAttendance->gym_member_id,
                            'module_type' => 'gym',
                            'attendable_type' => 'App\\Modules\\Gym\\Models\\GymSession', // Generic gym session
                            'attendable_id' => $gymAttendance->id, // Use attendance ID as fallback
                            'check_in_time' => $gymAttendance->check_in_time,
                            'check_out_time' => $gymAttendance->check_out_time,
                            'status' => $gymAttendance->status ?? 'checked_in',
                            'notes' => $gymAttendance->notes,
                            'created_at' => $gymAttendance->created_at,
                            'updated_at' => $gymAttendance->updated_at,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove migrated data (optional - be careful with this)
        if (Schema::hasTable('members')) {
            DB::table('members')->where('member_type', 'gym')->delete();
        }
        
        if (Schema::hasTable('member_subscriptions')) {
            DB::table('member_subscriptions')->where('module_type', 'gym')->delete();
        }
        
        if (Schema::hasTable('member_attendances')) {
            DB::table('member_attendances')->where('module_type', 'gym')->delete();
        }
    }
};
