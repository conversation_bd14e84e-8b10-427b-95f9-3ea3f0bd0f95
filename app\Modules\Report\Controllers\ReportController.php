<?php

namespace App\Modules\Report\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Report\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    /**
     * Display the reports index page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $reports = Report::where('tenant_id', Auth::user()->tenant_id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('report::index', compact('reports'));
    }

    /**
     * Show the form for creating a new report.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('report::create');
    }

    /**
     * Store a newly created report in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'parameters' => 'nullable|array'
        ]);

        $validated['tenant_id'] = Auth::user()->tenant_id;
        $validated['created_by'] = Auth::id();

        $report = Report::create($validated);

        return redirect()->route('reports.show', $report)
            ->with('success', 'Report created successfully.');
    }

    /**
     * Display the specified report.
     *
     * @param  \App\Modules\Report\Models\Report  $report
     * @return \Illuminate\View\View
     */
    public function show(Report $report)
    {
        if ($report->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        return view('report::show', compact('report'));
    }

    /**
     * Show the form for editing the specified report.
     *
     * @param  \App\Modules\Report\Models\Report  $report
     * @return \Illuminate\View\View
     */
    public function edit(Report $report)
    {
        if ($report->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        return view('report::edit', compact('report'));
    }

    /**
     * Update the specified report in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Report\Models\Report  $report
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Report $report)
    {
        if ($report->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'parameters' => 'nullable|array'
        ]);

        $validated['updated_by'] = Auth::id();

        $report->update($validated);

        return redirect()->route('reports.show', $report)
            ->with('success', 'Report updated successfully.');
    }

    /**
     * Remove the specified report from storage.
     *
     * @param  \App\Modules\Report\Models\Report  $report
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Report $report)
    {
        if ($report->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        $report->delete();

        return redirect()->route('reports.index')
            ->with('success', 'Report deleted successfully.');
    }

    /**
     * Generate the specified report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Report\Models\Report  $report
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generate(Request $request, Report $report)
    {
        if ($report->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        // TODO: Implement report generation logic here
        $report->update([
            'last_generated_at' => now(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('reports.show', $report)
            ->with('success', 'Report generated successfully.');
    }
}
