<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Report\Controllers\ReportController;

/*
|--------------------------------------------------------------------------
| Report Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth', 'verified'])->prefix('reports')->name('reports.')->group(function () {
    Route::get('/', [ReportController::class, 'index'])->name('index');
    Route::get('/create', [ReportController::class, 'create'])->name('create');
    Route::post('/', [ReportController::class, 'store'])->name('store');
    Route::get('/{report}', [ReportController::class, 'show'])->name('show');
    Route::get('/{report}/edit', [ReportController::class, 'edit'])->name('edit');
    Route::put('/{report}', [ReportController::class, 'update'])->name('update');
    Route::delete('/{report}', [ReportController::class, 'destroy'])->name('destroy');
    Route::post('/{report}/generate', [ReportController::class, 'generate'])->name('generate');
});
