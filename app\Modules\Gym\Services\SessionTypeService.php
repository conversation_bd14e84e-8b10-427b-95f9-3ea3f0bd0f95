<?php

namespace App\Modules\Gym\Services;

use App\Modules\Gym\Models\SessionType;
use Illuminate\Support\Str;

class SessionTypeService
{
    /**
     * Get session types for member selection.
     */
    public function getSessionTypesForMember(int $tenantId, ?string $category = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = SessionType::where('tenant_id', $tenantId)
            ->active()
            ->ordered();

        if ($category) {
            $query->byCategory($category);
        }

        return $query->get();
    }

    /**
     * Get popular session types.
     */
    public function getPopularSessionTypes(int $tenantId, int $limit = 6): \Illuminate\Database\Eloquent\Collection
    {
        return SessionType::where('tenant_id', $tenantId)
            ->active()
            ->popular()
            ->ordered()
            ->limit($limit)
            ->get();
    }

    /**
     * Get session types grouped by category.
     */
    public function getSessionTypesByCategory(int $tenantId): array
    {
        $sessionTypes = SessionType::where('tenant_id', $tenantId)
            ->active()
            ->ordered()
            ->get()
            ->groupBy('category');

        $categories = SessionType::getCategories();
        $result = [];

        foreach ($categories as $key => $name) {
            $result[$key] = [
                'name' => $name,
                'session_types' => $sessionTypes->get($key, collect())
            ];
        }

        return $result;
    }

    /**
     * Create a new session type.
     */
    public function createSessionType(int $tenantId, array $data): SessionType
    {
        $data['tenant_id'] = $tenantId;
        $data['slug'] = $this->generateUniqueSlug($tenantId, $data['name']);
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        return SessionType::create($data);
    }

    /**
     * Update an existing session type.
     */
    public function updateSessionType(SessionType $sessionType, array $data): SessionType
    {
        if (isset($data['name']) && $data['name'] !== $sessionType->name) {
            $data['slug'] = $this->generateUniqueSlug($sessionType->tenant_id, $data['name'], $sessionType->id);
        }

        $data['updated_by'] = auth()->id();

        $sessionType->update($data);
        return $sessionType->fresh();
    }

    /**
     * Generate unique slug for session type.
     */
    private function generateUniqueSlug(int $tenantId, string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($tenantId, $slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists.
     */
    private function slugExists(int $tenantId, string $slug, ?int $excludeId = null): bool
    {
        $query = SessionType::where('tenant_id', $tenantId)->where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get session type recommendations based on member profile.
     */
    public function getRecommendedSessionTypes(int $tenantId, array $memberProfile): \Illuminate\Database\Eloquent\Collection
    {
        $query = SessionType::where('tenant_id', $tenantId)->active();

        // Basic recommendations based on goals
        if (isset($memberProfile['goals'])) {
            $goals = is_array($memberProfile['goals']) ? $memberProfile['goals'] : [$memberProfile['goals']];
            
            if (in_array('weight_loss', $goals)) {
                $query->whereIn('category', ['fitness', 'nutrition']);
            }
            
            if (in_array('muscle_building', $goals)) {
                $query->where('category', 'fitness');
            }
            
            if (in_array('health_improvement', $goals)) {
                $query->whereIn('category', ['wellness', 'nutrition']);
            }
        }

        // Recommendations based on fitness level
        if (isset($memberProfile['fitness_level'])) {
            switch ($memberProfile['fitness_level']) {
                case 'beginner':
                    $query->whereJsonContains('target_audience', 'Beginners');
                    break;
                case 'intermediate':
                    $query->whereJsonContains('target_audience', 'Intermediate');
                    break;
                case 'advanced':
                    $query->whereJsonContains('target_audience', 'Advanced');
                    break;
            }
        }

        return $query->ordered()->limit(8)->get();
    }

    /**
     * Get session type with detailed information for booking.
     */
    public function getSessionTypeForBooking(int $sessionTypeId): ?SessionType
    {
        return SessionType::with(['tenant'])
            ->where('id', $sessionTypeId)
            ->active()
            ->first();
    }

    /**
     * Calculate session price with any applicable discounts.
     */
    public function calculateSessionPrice(SessionType $sessionType, ?array $discounts = null): array
    {
        $basePrice = $sessionType->default_price ?? 0;
        $finalPrice = $basePrice;
        $appliedDiscounts = [];

        if ($discounts) {
            foreach ($discounts as $discount) {
                if ($discount['type'] === 'percentage') {
                    $discountAmount = $basePrice * ($discount['value'] / 100);
                    $finalPrice -= $discountAmount;
                    $appliedDiscounts[] = [
                        'name' => $discount['name'],
                        'type' => 'percentage',
                        'value' => $discount['value'],
                        'amount' => $discountAmount
                    ];
                } elseif ($discount['type'] === 'fixed') {
                    $discountAmount = min($discount['value'], $finalPrice);
                    $finalPrice -= $discountAmount;
                    $appliedDiscounts[] = [
                        'name' => $discount['name'],
                        'type' => 'fixed',
                        'value' => $discount['value'],
                        'amount' => $discountAmount
                    ];
                }
            }
        }

        return [
            'base_price' => $basePrice,
            'final_price' => max(0, $finalPrice),
            'total_discount' => $basePrice - max(0, $finalPrice),
            'applied_discounts' => $appliedDiscounts
        ];
    }

    /**
     * Get session type statistics.
     */
    public function getSessionTypeStatistics(int $tenantId): array
    {
        $sessionTypes = SessionType::where('tenant_id', $tenantId)->get();

        return [
            'total_session_types' => $sessionTypes->count(),
            'active_session_types' => $sessionTypes->where('is_active', true)->count(),
            'popular_session_types' => $sessionTypes->where('is_popular', true)->count(),
            'categories' => $sessionTypes->groupBy('category')->map->count(),
            'average_duration' => $sessionTypes->avg('duration_minutes'),
            'average_price' => $sessionTypes->avg('default_price'),
        ];
    }
}
