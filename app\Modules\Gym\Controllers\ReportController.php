<?php

namespace App\Modules\Gym\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gym\Models\Member;
use App\Modules\Gym\Models\Subscription;
use App\Modules\Gym\Models\Attendance;
use App\Modules\Gym\Models\Equipment;
use App\Modules\Gym\Models\Trainer;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $tenantId = auth()->user()->tenant_id;

        return view('gym::dashboard', compact('tenantId'));
    }

    /**
     * Generate membership report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function membership(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        $startDate = $request->input('start_date', now()->startOfMonth());
        $endDate = $request->input('end_date', now()->endOfMonth());

        // Get membership statistics
        $totalMembers = Member::where('tenant_id', $tenantId)->count();
        $activeMembers = Member::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->count();
        $newMembers = Member::where('tenant_id', $tenantId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Get membership breakdown by type
        $membershipBreakdown = Subscription::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->with('membership')
            ->get()
            ->groupBy('membership.name')
            ->map(function ($subscriptions) {
                return $subscriptions->count();
            });

        return view('gym::reports.membership', compact(
            'totalMembers',
            'activeMembers',
            'newMembers',
            'membershipBreakdown',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Generate attendance report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function attendance(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        $startDate = $request->input('start_date', now()->startOfMonth());
        $endDate = $request->input('end_date', now()->endOfMonth());

        // Get attendance statistics
        $totalAttendance = Attendance::where('tenant_id', $tenantId)
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->count();

        $dailyAttendance = Attendance::where('tenant_id', $tenantId)
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->selectRaw('DATE(check_in_time) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $peakHours = Attendance::where('tenant_id', $tenantId)
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->selectRaw('HOUR(check_in_time) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->get();

        return view('gym::reports.attendance', compact(
            'totalAttendance',
            'dailyAttendance',
            'peakHours',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Generate revenue report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function revenue(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        $startDate = $request->input('start_date', now()->startOfMonth());
        $endDate = $request->input('end_date', now()->endOfMonth());

        // Get revenue statistics
        $totalRevenue = Subscription::where('tenant_id', $tenantId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('price');

        $monthlyRevenue = Subscription::where('tenant_id', $tenantId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(price) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        $revenueByMembership = Subscription::where('tenant_id', $tenantId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('membership')
            ->get()
            ->groupBy('membership.name')
            ->map(function ($subscriptions) {
                return $subscriptions->sum('price');
            });

        return view('gym::reports.revenue', compact(
            'totalRevenue',
            'monthlyRevenue',
            'revenueByMembership',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Generate equipment report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function equipment(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        // Get equipment statistics
        $totalEquipment = Equipment::where('tenant_id', $tenantId)->count();
        $workingEquipment = Equipment::where('tenant_id', $tenantId)
            ->where('status', 'working')
            ->count();
        $maintenanceEquipment = Equipment::where('tenant_id', $tenantId)
            ->where('status', 'maintenance')
            ->count();
        $brokenEquipment = Equipment::where('tenant_id', $tenantId)
            ->where('status', 'broken')
            ->count();

        // Get equipment by category
        $equipmentByCategory = Equipment::where('tenant_id', $tenantId)
            ->selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get();

        // Get equipment needing maintenance
        $equipmentNeedingMaintenance = Equipment::where('tenant_id', $tenantId)
            ->where(function ($query) {
                $query->where('status', 'maintenance')
                    ->orWhere('next_maintenance_date', '<=', now());
            })
            ->get();

        return view('gym::reports.equipment', compact(
            'totalEquipment',
            'workingEquipment',
            'maintenanceEquipment',
            'brokenEquipment',
            'equipmentByCategory',
            'equipmentNeedingMaintenance'
        ));
    }

    /**
     * Generate trainer report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function trainer(Request $request)
    {
        $tenantId = auth()->user()->tenant_id;

        $startDate = $request->input('start_date', now()->startOfMonth());
        $endDate = $request->input('end_date', now()->endOfMonth());

        // Get trainer statistics
        $totalTrainers = Trainer::where('tenant_id', $tenantId)->count();
        $activeTrainers = Trainer::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->count();

        // Get trainer performance (if you have classes or sessions)
        $trainerPerformance = Trainer::where('tenant_id', $tenantId)
            ->with(['classes' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->get()
            ->map(function ($trainer) {
                return [
                    'name' => $trainer->name,
                    'classes_count' => $trainer->classes->count(),
                    'hourly_rate' => $trainer->hourly_rate,
                ];
            });

        return view('gym::reports.trainer', compact(
            'totalTrainers',
            'activeTrainers',
            'trainerPerformance',
            'startDate',
            'endDate'
        ));
    }
}
