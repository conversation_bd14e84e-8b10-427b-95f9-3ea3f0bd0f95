<?php $__env->startSection('page-title', 'Class Schedule'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- <PERSON> Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Class Schedule</h1>
                    <p class="text-gray-600 mt-2">Manage your gym classes and schedules</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('gym.classes.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        Schedule Class
                    </a>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                        <i class="fas fa-calendar-alt text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($classes->count() ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($classes->where('is_active', true)->count() ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Today's Classes</p>
                        <p class="text-2xl font-bold text-gray-900">8</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-xl bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                        <i class="fas fa-percentage text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Avg. Attendance</p>
                        <p class="text-2xl font-bold text-gray-900">85%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 mb-8">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Classes</h3>
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input type="text" placeholder="Search classes..." class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Trainer</label>
                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors">
                            <option value="">All Trainers</option>
                            <?php $__currentLoopData = $trainers ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trainer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($trainer->id); ?>"><?php echo e($trainer->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Time</label>
                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors">
                            <option value="">All Times</option>
                            <option value="morning">Morning (06:00-12:00)</option>
                            <option value="afternoon">Afternoon (12:00-18:00)</option>
                            <option value="evening">Evening (18:00-22:00)</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg w-full font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Classes Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <?php $__empty_1 = true; $__currentLoopData = $classes ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-300 group">
                    <!-- Class Header -->
                    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-200 p-6">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-bold text-gray-900"><?php echo e($class->name ?? 'Class Name'); ?></h3>
                                <p class="text-gray-600 text-sm mt-1 line-clamp-2"><?php echo e($class->description ?? 'Class description'); ?></p>
                            </div>
                            <?php if($class->is_active ?? true): ?>
                                <span class="bg-green-100 text-green-800 text-xs font-bold px-3 py-1 rounded-full">
                                    <i class="fas fa-check-circle mr-1"></i>ACTIVE
                                </span>
                            <?php else: ?>
                                <span class="bg-red-100 text-red-800 text-xs font-bold px-3 py-1 rounded-full">
                                    <i class="fas fa-times-circle mr-1"></i>INACTIVE
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Class Details -->
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-clock text-sm"></i>
                                </div>
                                <span class="text-sm text-gray-700 font-medium">
                                    <?php echo e(substr($class->start_time ?? '07:00', 0, 5)); ?> - <?php echo e(substr($class->end_time ?? '08:00', 0, 5)); ?>

                                    <span class="text-gray-500">(<?php echo e($class->duration_minutes ?? 60); ?> min)</span>
                                </span>
                            </div>

                            <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-calendar text-sm"></i>
                                </div>
                                <span class="text-sm text-gray-700 font-medium">
                                    <?php echo e($class->getDaysOfWeekString() ?: 'Mon, Wed, Fri'); ?>

                                </span>
                            </div>

                            <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-map-marker-alt text-sm"></i>
                                </div>
                                <span class="text-sm text-gray-700 font-medium"><?php echo e($class->location ?? 'Studio A'); ?></span>
                            </div>

                            <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-users text-sm"></i>
                                </div>
                                <span class="text-sm text-gray-700 font-medium">Capacity: <?php echo e($class->capacity ?? 20); ?> people</span>
                            </div>

                            <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-user-tie text-sm"></i>
                                </div>
                                <span class="text-sm text-gray-700 font-medium"><?php echo e($class->trainer->name ?? 'Trainer Name'); ?></span>
                            </div>

                            <?php if($class->price ?? false): ?>
                                <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100">
                                    <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-yellow-500 to-yellow-600 flex items-center justify-center text-white mr-3">
                                        <i class="fas fa-dollar-sign text-sm"></i>
                                    </div>
                                    <span class="text-sm text-gray-700 font-medium">Rp <?php echo e(number_format($class->price, 0, ',', '.')); ?> per class</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Attendance Progress -->
                        <div class="mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
                            <?php
                                // Get total bookings for this class from upcoming schedules
                                $totalBookings = 15; // Placeholder data
                                $percentage = $class->capacity > 0 ? min(($totalBookings / ($class->capacity ?? 20)) * 100, 100) : 0;
                            ?>
                            <div class="flex justify-between text-sm text-gray-700 mb-2 font-medium">
                                <span>Current Bookings</span>
                                <span><?php echo e($totalBookings); ?>/<?php echo e($class->capacity ?? 20); ?></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 h-3 rounded-full transition-all duration-300" style="width: <?php echo e($percentage); ?>%"></div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1"><?php echo e(number_format($percentage, 1)); ?>% capacity</div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200 flex justify-between items-center">
                        <a href="<?php echo e(route('gym.classes.show', $class)); ?>" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors flex items-center">
                            <i class="fas fa-eye mr-2"></i>View Details
                        </a>
                        <div class="flex space-x-3">
                            <a href="<?php echo e(route('gym.classes.attendance', $class)); ?>" class="text-green-600 hover:text-green-800 text-sm font-medium transition-colors flex items-center">
                                <i class="fas fa-calendar mr-1"></i>Schedules
                            </a>
                            <a href="<?php echo e(route('gym.classes.edit', $class)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors flex items-center">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </a>
                            <form action="<?php echo e(route('gym.classes.destroy', $class)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this class?');">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium transition-colors flex items-center">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-6">
                            <i class="fas fa-calendar-alt text-4xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">No Classes Scheduled</h3>
                        <p class="text-gray-600 mb-6">Create your first class schedule to get started with group fitness sessions.</p>
                        <a href="<?php echo e(route('gym.classes.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Schedule First Class
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if(isset($classes) && method_exists($classes, 'links')): ?>
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 px-6 py-4">
                <?php echo e($classes->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/classes/index.blade.php ENDPATH**/ ?>