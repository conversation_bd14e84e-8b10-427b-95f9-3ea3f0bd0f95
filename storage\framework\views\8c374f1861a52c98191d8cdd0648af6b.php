<!-- Module Request Modal -->
<div id="request-module-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Request Module Activation</h3>
            <button type="button" onclick="closeRequestModal()" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="module-request-form" action="#" method="POST">
            <?php echo csrf_field(); ?>
            
            <div class="mb-4">
                <p class="text-gray-700">You are requesting activation for: <span id="module-name" class="font-semibold"></span></p>
            </div>
            
            <div class="mb-4">
                <label for="request-reason" class="block text-sm font-medium text-gray-700">Reason for Request <span class="text-red-500">*</span></label>
                <textarea id="request-reason" name="reason" rows="3" required
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    placeholder="Please explain why you need this module..."></textarea>
            </div>
            
            <div class="mb-4">
                <label for="request-duration" class="block text-sm font-medium text-gray-700">Requested Duration</label>
                <select id="request-duration" name="duration"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <option value="1">1 Month</option>
                    <option value="3">3 Months</option>
                    <option value="6">6 Months</option>
                    <option value="12" selected>1 Year</option>
                </select>
            </div>
            
            <div class="flex justify-end">
                <button type="button" onclick="closeRequestModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Cancel
                </button>
                <button type="button" onclick="submitModuleRequest()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Submit Request
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Module Purchase Modal -->
<div id="purchase-module-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Purchase Module</h3>
            <button type="button" onclick="closePurchaseModal()" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="mb-6 bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900" id="purchase-module-name">Module Name</h4>
                    <p class="text-sm text-gray-600" id="purchase-module-price">Rp 199.000/month</p>
                </div>
                <div class="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    Best Value
                </div>
            </div>
        </div>
        
        <form id="module-purchase-form" action="#" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" id="purchase-module-id" name="module_id">
            
            <div class="mb-4">
                <label for="billing-cycle" class="block text-sm font-medium text-gray-700">Billing Cycle</label>
                <select id="billing-cycle" name="billing_cycle" onchange="updatePrice()"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly (10% off)</option>
                    <option value="annually">Annually (20% off)</option>
                </select>
            </div>
            
            <div class="mb-4">
                <label for="payment-method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                <div class="mt-2 grid grid-cols-3 gap-3">
                    <div>
                        <input type="radio" id="payment-method-cc" name="payment_method" value="credit_card" class="sr-only" checked>
                        <label for="payment-method-cc" class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 peer-checked:border-indigo-500 peer-checked:bg-indigo-50">
                            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="mt-2 text-xs font-medium text-gray-900">Credit Card</span>
                        </label>
                    </div>
                    <div>
                        <input type="radio" id="payment-method-bank" name="payment_method" value="bank_transfer" class="sr-only">
                        <label for="payment-method-bank" class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 peer-checked:border-indigo-500 peer-checked:bg-indigo-50">
                            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                            </svg>
                            <span class="mt-2 text-xs font-medium text-gray-900">Bank Transfer</span>
                        </label>
                    </div>
                    <div>
                        <input type="radio" id="payment-method-ewallet" name="payment_method" value="e_wallet" class="sr-only">
                        <label for="payment-method-ewallet" class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer hover:bg-gray-50 peer-checked:border-indigo-500 peer-checked:bg-indigo-50">
                            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="mt-2 text-xs font-medium text-gray-900">E-Wallet</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">Subtotal</span>
                    <span class="text-sm font-medium text-gray-900" id="purchase-subtotal">Rp 199.000</span>
                </div>
                <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">Discount</span>
                    <span class="text-sm font-medium text-green-600" id="purchase-discount">- Rp 0</span>
                </div>
                <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">Tax (11%)</span>
                    <span class="text-sm font-medium text-gray-900" id="purchase-tax">Rp 21.890</span>
                </div>
                <div class="flex justify-between pt-2 border-t border-gray-200">
                    <span class="text-base font-medium text-gray-900">Total</span>
                    <span class="text-base font-bold text-gray-900" id="purchase-total">Rp 220.890</span>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="button" onclick="closePurchaseModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Cancel
                </button>
                <button type="button" onclick="processPurchase()" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">
                    Complete Purchase
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Module Marketplace Modal -->
<div id="module-marketplace-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-5xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-medium text-gray-900">Module Marketplace</h3>
            <button type="button" onclick="closeMarketplaceModal()" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="mb-6">
            <div class="flex flex-col md:flex-row justify-between items-center bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
                <div>
                    <h4 class="text-xl font-bold mb-2">Enhance Your Business with Premium Modules</h4>
                    <p class="text-indigo-100">Choose from our wide range of specialized modules to boost your business capabilities.</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <span class="inline-block bg-white text-indigo-600 px-4 py-2 rounded-lg font-bold">Special Offers Available</span>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- All modules would be listed here dynamically -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                <div class="p-4 bg-green-500 text-white">
                    <div class="flex justify-between items-center">
                        <h4 class="text-lg font-semibold">Point of Sale</h4>
                        <span class="bg-white text-green-500 text-xs font-bold px-2 py-1 rounded">POPULAR</span>
                    </div>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <span class="text-2xl font-bold">Rp 299.000</span>
                        <span class="text-gray-500 text-sm">/month</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Complete point of sale system with inventory management and sales reporting.</p>
                    <button onclick="showPurchaseModal('Point of Sale', 299000, 'monthly')" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded text-sm">
                        Purchase Now
                    </button>
                </div>
            </div>
            
            <!-- More modules would be listed here -->
        </div>
    </div>
</div>

<script>
    // Current module price for calculations
    let currentModulePrice = 0;
    let currentModuleName = '';
    let currentBillingCycle = 'monthly';
    
    // Request Modal Functions
    function showRequestModal(moduleName) {
        document.getElementById('module-name').textContent = moduleName;
        document.getElementById('request-module-modal').classList.remove('hidden');
    }
    
    function closeRequestModal() {
        document.getElementById('request-module-modal').classList.add('hidden');
    }
    
    function submitModuleRequest() {
        // In a real application, this would submit the form to the server
        // For now, we'll just show a success message and close the modal
        alert('Your request has been submitted. The administrator will review it shortly.');
        closeRequestModal();
    }
    
    // Purchase Modal Functions
    function showPurchaseModal(moduleName, price, billingCycle) {
        currentModulePrice = price;
        currentModuleName = moduleName;
        currentBillingCycle = billingCycle;
        
        document.getElementById('purchase-module-name').textContent = moduleName;
        document.getElementById('purchase-module-price').textContent = formatCurrency(price) + '/' + (billingCycle === 'monthly' ? 'month' : billingCycle === 'quarterly' ? 'quarter' : 'year');
        document.getElementById('billing-cycle').value = billingCycle;
        
        updatePrice();
        document.getElementById('purchase-module-modal').classList.remove('hidden');
    }
    
    function closePurchaseModal() {
        document.getElementById('purchase-module-modal').classList.add('hidden');
    }
    
    function updatePrice() {
        const billingCycle = document.getElementById('billing-cycle').value;
        let price = currentModulePrice;
        let discount = 0;
        
        if (billingCycle === 'quarterly') {
            // 10% discount for quarterly billing
            discount = price * 3 * 0.1;
            price = price * 3 - discount;
        } else if (billingCycle === 'annually') {
            // 20% discount for annual billing
            discount = price * 12 * 0.2;
            price = price * 12 - discount;
        }
        
        const tax = price * 0.11;
        const total = price + tax;
        
        document.getElementById('purchase-subtotal').textContent = formatCurrency(price);
        document.getElementById('purchase-discount').textContent = '- ' + formatCurrency(discount);
        document.getElementById('purchase-tax').textContent = formatCurrency(tax);
        document.getElementById('purchase-total').textContent = formatCurrency(total);
    }
    
    function processPurchase() {
        // In a real application, this would process the payment
        // For now, we'll just show a success message and close the modal
        alert('Thank you for your purchase! Your module has been activated.');
        closePurchaseModal();
        
        // Reload the page to show the newly activated module
        // In a real application, this would be handled more elegantly
        // window.location.reload();
    }
    
    // Marketplace Modal Functions
    function showModuleMarketplace() {
        document.getElementById('module-marketplace-modal').classList.remove('hidden');
    }
    
    function closeMarketplaceModal() {
        document.getElementById('module-marketplace-modal').classList.add('hidden');
    }
    
    function showAllModules() {
        showModuleMarketplace();
    }
    
    // Helper Functions
    function formatCurrency(amount) {
        return 'Rp ' + amount.toLocaleString('id-ID');
    }
</script>
<?php /**PATH C:\xampp\htdocs\app\Modules/Auth/Resources/views/partials/module-modals.blade.php ENDPATH**/ ?>