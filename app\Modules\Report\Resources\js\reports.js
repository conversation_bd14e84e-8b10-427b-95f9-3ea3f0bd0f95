import 'daterangepicker';
import '@fortawesome/fontawesome-free/css/all.min.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'daterangepicker/daterangepicker.css';

// Initialize date range pickers
document.addEventListener('DOMContentLoaded', function() {
    const dateRanges = document.querySelectorAll('.daterangepicker');
    dateRanges.forEach(input => {
        $(input).daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'Clear',
                format: 'YYYY-MM-DD'
            }
        });

        $(input).on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        });

        $(input).on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    });
});

// Dynamic parameter fields handler
const parameterTemplates = {
    sales: [
        { name: 'date_range', type: 'daterange', label: 'Date Range' },
        { name: 'product_category', type: 'select', label: 'Product Category', options: [] }
    ],
    inventory: [
        { name: 'warehouse', type: 'select', label: 'Warehouse', options: [] },
        { name: 'stock_status', type: 'select', label: 'Stock Status', options: [
            { value: 'in_stock', label: 'In Stock' },
            { value: 'low_stock', label: 'Low Stock' },
            { value: 'out_of_stock', label: 'Out of Stock' }
        ]}
    ],
    customer: [
        { name: 'customer_group', type: 'select', label: 'Customer Group', options: [] },
        { name: 'activity_period', type: 'select', label: 'Activity Period', options: [
            { value: 'last_7_days', label: 'Last 7 Days' },
            { value: 'last_30_days', label: 'Last 30 Days' },
            { value: 'last_90_days', label: 'Last 90 Days' },
            { value: 'last_365_days', label: 'Last 365 Days' }
        ]}
    ],
    financial: [
        { name: 'period', type: 'select', label: 'Period', options: [
            { value: 'daily', label: 'Daily' },
            { value: 'weekly', label: 'Weekly' },
            { value: 'monthly', label: 'Monthly' },
            { value: 'quarterly', label: 'Quarterly' },
            { value: 'yearly', label: 'Yearly' }
        ]},
        { name: 'report_type', type: 'select', label: 'Report Type', options: [
            { value: 'revenue', label: 'Revenue Report' },
            { value: 'expenses', label: 'Expenses Report' },
            { value: 'profit_loss', label: 'Profit & Loss' },
            { value: 'cash_flow', label: 'Cash Flow' }
        ]}
    ]
};

function loadDynamicOptions() {
    // Load product categories
    fetch('/api/product-categories')
        .then(response => response.json())
        .then(data => {
            parameterTemplates.sales[1].options = data.map(category => ({
                value: category.id,
                label: category.name
            }));
        })
        .catch(error => console.error('Error loading product categories:', error));

    // Load warehouses
    fetch('/api/warehouses')
        .then(response => response.json())
        .then(data => {
            parameterTemplates.inventory[0].options = data.map(warehouse => ({
                value: warehouse.id,
                label: warehouse.name
            }));
        })
        .catch(error => console.error('Error loading warehouses:', error));

    // Load customer groups
    fetch('/api/customer-groups')
        .then(response => response.json())
        .then(data => {
            parameterTemplates.customer[0].options = data.map(group => ({
                value: group.id,
                label: group.name
            }));
        })
        .catch(error => console.error('Error loading customer groups:', error));
}

// Update parameter fields based on report type
function updateParameters(type, container, savedParameters = {}) {
    container.innerHTML = '';

    if (type && parameterTemplates[type]) {
        parameterTemplates[type].forEach(param => {
            const div = document.createElement('div');
            div.className = 'mb-3';
            const savedValue = savedParameters[param.name] || '';

            let inputHtml = '';
            if (param.type === 'daterange') {
                inputHtml = `<input type="text" class="form-control daterangepicker" name="parameters[${param.name}]" value="${savedValue}">`;
            } else if (param.type === 'select') {
                inputHtml = `<select class="form-control" name="parameters[${param.name}]">
                    <option value="">Select ${param.label}</option>
                    ${(param.options || []).map(opt =>
                        `<option value="${opt.value}" ${savedValue === opt.value ? 'selected' : ''}>${opt.label}</option>`
                    ).join('')}
                </select>`;
            }

            div.innerHTML = `
                <label class="form-label">${param.label}</label>
                ${inputHtml}
            `;
            container.appendChild(div);
        });

        // Reinitialize date range pickers
        const dateRanges = container.querySelectorAll('.daterangepicker');
        dateRanges.forEach(input => {
            $(input).daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: 'Clear',
                    format: 'YYYY-MM-DD'
                }
            });

            $(input).on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $(input).on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        });
    }
}

// Initialize parameter handling when document is ready
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const parametersList = document.querySelector('.parameters-list');

    if (typeSelect && parametersList) {
        // Load dynamic options
        loadDynamicOptions();

        // Initial parameters setup
        const savedParameters = parametersList.dataset.parameters
            ? JSON.parse(parametersList.dataset.parameters)
            : {};

        if (typeSelect.value) {
            updateParameters(typeSelect.value, parametersList, savedParameters);
        }

        // Handle type changes
        typeSelect.addEventListener('change', function() {
            updateParameters(this.value, parametersList, {});
        });
    }
});
