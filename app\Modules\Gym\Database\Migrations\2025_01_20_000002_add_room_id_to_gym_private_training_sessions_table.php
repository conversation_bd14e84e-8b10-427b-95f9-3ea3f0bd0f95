<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('gym_private_training_sessions')) {
            Schema::table('gym_private_training_sessions', function (Blueprint $table) {
                if (!Schema::hasColumn('gym_private_training_sessions', 'room_id')) {
                    $table->foreignId('room_id')->nullable()->after('gym_trainer_id')->constrained('gym_rooms')->nullOnDelete();
                    $table->index(['tenant_id', 'room_id']);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('gym_private_training_sessions')) {
            Schema::table('gym_private_training_sessions', function (Blueprint $table) {
                if (Schema::hasColumn('gym_private_training_sessions', 'room_id')) {
                    $table->dropForeign(['room_id']);
                    $table->dropIndex(['tenant_id', 'room_id']);
                    $table->dropColumn('room_id');
                }
            });
        }
    }
};
