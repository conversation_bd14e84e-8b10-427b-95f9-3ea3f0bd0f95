<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?></title>

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="House of Pals">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="House of Pals">
    <meta name="description" content="Complete SaaS POS system for gym, spa, restaurant and retail businesses">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#4f46e5">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-152x152.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/icon-16x16.png">
    <link rel="mask-icon" href="/images/icons/safari-pinned-tab.svg" color="#4f46e5">
    <link rel="shortcut icon" href="/favicon.ico">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Tailwind CSS CDN (fallback) -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>

    <!-- PWA Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);

                        // Check for updates
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, show update notification
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        let installButton = null;

        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('beforeinstallprompt fired');
            e.preventDefault();
            deferredPrompt = e;
            showInstallButton();
        });

        window.addEventListener('appinstalled', (evt) => {
            console.log('App installed successfully');
            hideInstallButton();
        });

        function showInstallButton() {
            if (!installButton) {
                installButton = document.createElement('button');
                installButton.innerHTML = '<i class="fas fa-download mr-2"></i>Install App';
                installButton.className = 'fixed bottom-20 right-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
                installButton.onclick = installApp;
                document.body.appendChild(installButton);
            }
        }

        function hideInstallButton() {
            if (installButton) {
                installButton.remove();
                installButton = null;
            }
        }

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    deferredPrompt = null;
                    hideInstallButton();
                });
            }
        }

        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Update Available</h4>
                            <p class="text-sm opacity-90">A new version is ready to install.</p>
                        </div>
                        <button onclick="updateApp()" class="ml-4 bg-white text-blue-600 px-3 py-1 rounded text-sm">
                            Update
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(notification);

            // Auto-hide after 10 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        function updateApp() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration && registration.waiting) {
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                    }
                });
            }
        }
    </script>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="h-screen flex flex-col">
        <!-- Fixed Top Navigation -->
        <nav class="bg-white border-b border-gray-200 shadow-sm fixed top-0 left-0 right-0 z-30">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <!-- Logo -->
                        <div class="flex items-center">
                            <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                                <img src="<?php echo e(asset('images/logo.jpg')); ?>" alt="House of Pals" class="h-8 w-auto">
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <div class="hidden space-x-8 sm:ml-10 sm:flex">
                            <?php if(auth()->guard()->check()): ?>
                                <a href="/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->is('dashboard') ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Dashboard
                                </a>
                                <a href="/upgrade" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->is('upgrade*') ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out">
                                    <i class="fas fa-rocket mr-2"></i>
                                    Upgrade
                                </a>
                                <?php if(Auth::user()->hasRole(['admin', 'super-admin'])): ?>
                                    <a href="/admin/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->is('admin*') ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out">
                                        <i class="fas fa-shield-alt mr-2"></i>
                                        Admin Panel
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Right Side -->
                    <div class="hidden sm:flex sm:items-center sm:space-x-4">
                        <?php if(auth()->guard()->check()): ?>
                            <!-- Notifications -->
                            <button class="text-gray-500 hover:text-gray-700 relative">
                                <i class="fas fa-bell text-lg"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">2</span>
                            </button>

                            <!-- User Menu -->
                            <div class="flex items-center space-x-3">
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-700"><?php echo e(Auth::user()->name); ?></div>
                                    <div class="text-xs text-gray-500">System Admin</div>
                                </div>
                                <div class="h-8 w-8 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                </div>
                                <form method="POST" action="/logout" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="text-gray-500 hover:text-gray-700">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </button>
                                </form>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center space-x-4">
                                <a href="/login" class="text-sm text-gray-700 hover:text-gray-900">
                                    <i class="fas fa-sign-in-alt mr-1"></i>Login
                                </a>
                                <a href="/register" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm">
                                    <i class="fas fa-user-plus mr-1"></i>Register
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="-mr-2 flex items-center sm:hidden">
                        <button class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out">
                            <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <div class="flex-1 pt-16 pb-16">
            <!-- Page Heading -->
            <?php if(isset($header)): ?>
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo e($header); ?>

                    </div>
                </header>
            <?php endif; ?>

            <!-- Scrollable Page Content -->
            <main class="h-full overflow-y-auto custom-scrollbar">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>

        <!-- Fixed Footer -->
        <footer class="bg-white border-t border-gray-200 fixed bottom-0 left-0 right-0 z-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <div>
                        © <?php echo e(date('Y')); ?> <?php echo e(config('app.name', 'Laravel')); ?>. All rights reserved.
                    </div>
                    <div class="flex items-center space-x-4">
                        <span>Version 1.0.0</span>
                        <span>•</span>
                        <a href="#" class="hover:text-gray-700">Support</a>
                        <span>•</span>
                        <a href="#" class="hover:text-gray-700">Documentation</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\resources\views/layouts/app.blade.php ENDPATH**/ ?>