<?php

namespace App\Modules\Gym\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Modules\Tenant\Models\Tenant;

class Room extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'gym_rooms';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'description',
        'capacity',
        'room_type',
        'location',
        'amenities',
        'hourly_rate',
        'is_active',
        'is_bookable',
        'booking_advance_days',
        'minimum_booking_duration',
        'maximum_booking_duration',
        'operating_hours',
        'notes',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'capacity' => 'integer',
        'hourly_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'is_bookable' => 'boolean',
        'booking_advance_days' => 'integer',
        'minimum_booking_duration' => 'integer',
        'maximum_booking_duration' => 'integer',
        'amenities' => 'array',
        'operating_hours' => 'array',
    ];

    /**
     * Get the tenant that owns the room.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who created the room.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who last updated the room.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * Get the private training sessions for this room.
     */
    public function privateTrainingSessions(): HasMany
    {
        return $this->hasMany(PrivateTrainingSession::class, 'room_id');
    }

    /**
     * Get the class schedules for this room.
     */
    public function classSchedules(): HasMany
    {
        return $this->hasMany(ClassSchedule::class, 'room_id');
    }

    /**
     * Scope a query to only include active rooms.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include bookable rooms.
     */
    public function scopeBookable($query)
    {
        return $query->where('is_bookable', true);
    }

    /**
     * Scope a query to filter by room type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('room_type', $type);
    }

    /**
     * Get the room's availability status.
     */
    public function getAvailabilityStatusAttribute()
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if (!$this->is_bookable) {
            return 'not_bookable';
        }

        // Check if room has any ongoing sessions
        $now = now();
        $ongoingSessions = $this->privateTrainingSessions()
            ->where('scheduled_date', $now->toDateString())
            ->where('scheduled_start_time', '<=', $now->toTimeString())
            ->where('scheduled_end_time', '>=', $now->toTimeString())
            ->where('status', 'scheduled')
            ->count();

        if ($ongoingSessions > 0) {
            return 'occupied';
        }

        return 'available';
    }

    /**
     * Get formatted amenities list.
     */
    public function getFormattedAmenitiesAttribute()
    {
        if (empty($this->amenities)) {
            return 'No amenities listed';
        }

        return implode(', ', $this->amenities);
    }

    /**
     * Get formatted operating hours.
     */
    public function getFormattedOperatingHoursAttribute()
    {
        if (empty($this->operating_hours)) {
            return '24/7';
        }

        $hours = [];
        foreach ($this->operating_hours as $day => $time) {
            if (!empty($time['open']) && !empty($time['close'])) {
                $hours[] = ucfirst($day) . ': ' . $time['open'] . ' - ' . $time['close'];
            }
        }

        return implode(', ', $hours);
    }

    /**
     * Check if room is available at specific date and time.
     */
    public function isAvailableAt($date, $startTime, $endTime)
    {
        if (!$this->is_active || !$this->is_bookable) {
            return false;
        }

        // Check for conflicting sessions
        $conflicts = $this->privateTrainingSessions()
            ->where('scheduled_date', $date)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('scheduled_start_time', [$startTime, $endTime])
                    ->orWhereBetween('scheduled_end_time', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('scheduled_start_time', '<=', $startTime)
                          ->where('scheduled_end_time', '>=', $endTime);
                    });
            })
            ->where('status', '!=', 'cancelled')
            ->count();

        return $conflicts === 0;
    }

    /**
     * Get room type options.
     */
    public static function getRoomTypes()
    {
        return [
            'private_training' => 'Private Training Room',
            'group_class' => 'Group Class Room',
            'cardio' => 'Cardio Area',
            'strength' => 'Strength Training Area',
            'functional' => 'Functional Training Area',
            'yoga' => 'Yoga/Pilates Studio',
            'spinning' => 'Spinning Room',
            'boxing' => 'Boxing/Martial Arts Room',
            'dance' => 'Dance Studio',
            'recovery' => 'Recovery/Stretching Area',
            'locker' => 'Locker Room',
            'office' => 'Office/Meeting Room',
            'storage' => 'Storage Room',
            'other' => 'Other',
        ];
    }

    /**
     * Get amenity options.
     */
    public static function getAmenityOptions()
    {
        return [
            'air_conditioning' => 'Air Conditioning',
            'sound_system' => 'Sound System',
            'mirrors' => 'Wall Mirrors',
            'mats' => 'Exercise Mats',
            'towels' => 'Towel Service',
            'water_dispenser' => 'Water Dispenser',
            'lockers' => 'Personal Lockers',
            'shower' => 'Shower Facilities',
            'wifi' => 'WiFi Access',
            'tv_screen' => 'TV/Display Screen',
            'projector' => 'Projector',
            'whiteboard' => 'Whiteboard',
            'first_aid' => 'First Aid Kit',
            'emergency_button' => 'Emergency Button',
            'cctv' => 'CCTV Monitoring',
        ];
    }
}
