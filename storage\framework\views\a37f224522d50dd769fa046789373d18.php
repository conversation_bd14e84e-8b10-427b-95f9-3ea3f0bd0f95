<?php $__env->startSection('page-title', 'Gym Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Welcome Message -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-2xl font-semibold text-gray-800 mb-2">Welcome to Gym Management</h2>
        <p class="text-gray-600">Manage your gym operations efficiently with our comprehensive dashboard.</p>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Total Members -->
        <div class="bg-blue-50 rounded-lg p-4 shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-500 text-white mr-4">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <p class="text-sm text-blue-800 font-medium">Total Members</p>
                    <p class="text-2xl font-bold text-blue-900"><?php echo e($totalMembers); ?></p>
                </div>
            </div>
        </div>

        <!-- Active Members -->
        <div class="bg-green-50 rounded-lg p-4 shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-500 text-white mr-4">
                    <i class="fas fa-user-check"></i>
                </div>
                <div>
                    <p class="text-sm text-green-800 font-medium">Active Members</p>
                    <p class="text-2xl font-bold text-green-900"><?php echo e($activeMembers); ?></p>
                </div>
            </div>
        </div>

        <!-- Today's Attendance -->
        <div class="bg-purple-50 rounded-lg p-4 shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-500 text-white mr-4">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <div>
                    <p class="text-sm text-purple-800 font-medium">Today's Attendance</p>
                    <p class="text-2xl font-bold text-purple-900"><?php echo e($todayAttendance); ?></p>
                </div>
            </div>
        </div>

        <!-- Expiring Subscriptions -->
        <div class="bg-yellow-50 rounded-lg p-4 shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-500 text-white mr-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <p class="text-sm text-yellow-800 font-medium">Expiring Soon</p>
                    <p class="text-2xl font-bold text-yellow-900"><?php echo e($expiringSubscriptions->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="/gym/members/create" class="bg-blue-50 hover:bg-blue-100 rounded-lg p-4 text-center transition duration-200">
                <i class="fas fa-user-plus text-2xl text-blue-600 mb-2"></i>
                <p class="text-sm font-medium text-gray-800">Add Member</p>
            </a>

            <a href="/gym/attendance/create" class="bg-green-50 hover:bg-green-100 rounded-lg p-4 text-center transition duration-200">
                <i class="fas fa-clipboard-check text-2xl text-green-600 mb-2"></i>
                <p class="text-sm font-medium text-gray-800">Record Attendance</p>
            </a>

            <a href="/gym/classes/create" class="bg-purple-50 hover:bg-purple-100 rounded-lg p-4 text-center transition duration-200">
                <i class="fas fa-calendar-plus text-2xl text-purple-600 mb-2"></i>
                <p class="text-sm font-medium text-gray-800">Schedule Class</p>
            </a>

            <a href="/gym/subscriptions/create" class="bg-yellow-50 hover:bg-yellow-100 rounded-lg p-4 text-center transition duration-200">
                <i class="fas fa-credit-card text-2xl text-yellow-600 mb-2"></i>
                <p class="text-sm font-medium text-gray-800">New Subscription</p>
            </a>
        </div>
    </div>

    <!-- Recent Activity & Upcoming Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Upcoming Classes -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-medium text-gray-800">Upcoming Classes</h3>
            </div>
            <div class="p-6">
                <?php if($upcomingClasses->isEmpty()): ?>
                    <p class="text-gray-500 text-center py-4">No upcoming classes scheduled.</p>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $upcomingClasses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                                <div>
                                    <p class="font-medium text-gray-800"><?php echo e($schedule->gymClass->name ?? 'Class Name'); ?></p>
                                    <p class="text-sm text-gray-600">Today • 10:00 - 11:00</p>
                                </div>
                                <a href="/gym/classes" class="text-blue-600 hover:text-blue-800 text-sm">Manage</a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
                <div class="mt-4 text-right">
                    <a href="/gym/classes" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All Classes →
                    </a>
                </div>
            </div>
        </div>

        <!-- Expiring Subscriptions -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-medium text-gray-800">Expiring Subscriptions</h3>
            </div>
            <div class="p-6">
                <?php if($expiringSubscriptions->isEmpty()): ?>
                    <p class="text-gray-500 text-center py-4">No subscriptions expiring soon.</p>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $expiringSubscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                                <div>
                                    <p class="font-medium text-gray-800">Member Name</p>
                                    <p class="text-sm text-gray-600">Expires in 3 days</p>
                                </div>
                                <a href="/gym/subscriptions" class="text-blue-600 hover:text-blue-800 text-sm">Renew</a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
                <div class="mt-4 text-right">
                    <a href="/gym/subscriptions" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All Subscriptions →
                    </a>
                </div>
            </div>
        </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/dashboard.blade.php ENDPATH**/ ?>