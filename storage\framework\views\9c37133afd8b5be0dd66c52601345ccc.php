<?php $__env->startSection('page-title', 'Gym Equipment'); ?>

<?php $__env->startSection('content'); ?>

                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800">Equipment</h2>
                    <a href="<?php echo e(route('gym.equipment.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
                        Add New Equipment
                    </a>
                </div>

                <!-- Search and Filters -->
                <div class="mb-6">
                    <!-- Search and Filters -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
        <i class="fas fa-filter mr-2 text-indigo-600"></i>
        Filter Gym Equipment
    </h3>
    <form action="<?php echo e(route('gym.equipment.index')); ?>" method="GET" class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search by name, serial number or location"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div class="w-full md:w-48">
                            <select name="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Status</option>
                                <option value="operational" <?php echo e(request('status') == 'operational' ? 'selected' : ''); ?>>Operational</option>
                                <option value="maintenance" <?php echo e(request('status') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                                <option value="out_of_order" <?php echo e(request('status') == 'out_of_order' ? 'selected' : ''); ?>>Out of Order</option>
                            </select>
                        </div>
                        <div class="w-full md:w-48">
                            <select name="category_id" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="w-full md:w-48">
                            <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="created_at" <?php echo e(request('sort') == 'created_at' ? 'selected' : ''); ?>>Date Added</option>
                                <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Name</option>
                                <option value="purchase_date" <?php echo e(request('sort') == 'purchase_date' ? 'selected' : ''); ?>>Purchase Date</option>
                                <option value="purchase_price" <?php echo e(request('sort') == 'purchase_price' ? 'selected' : ''); ?>>Price</option>
                            </select>
                        </div>
                        <div class="w-full md:w-48">
                            <select name="direction" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="desc" <?php echo e(request('direction') == 'desc' ? 'selected' : ''); ?>>Descending</option>
                                <option value="asc" <?php echo e(request('direction') == 'asc' ? 'selected' : ''); ?>>Ascending</option>
                            </select>
                        </div>
                        <div>
                            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
                                Filter
                            </button>
                        </div>
                    </form>
</div>
                </div>

                <!-- Equipment Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__empty_1 = true; $__currentLoopData = $equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                            <div class="relative">
                                <?php if($item->photo): ?>
                                    <img src="<?php echo e(Storage::url($item->photo)); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-48 object-cover">
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>

                                <div class="absolute top-2 right-2">
                                    <?php if($item->status === 'operational'): ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Operational</span>
                                    <?php elseif($item->status === 'maintenance'): ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Maintenance</span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Out of Order</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="p-4">
                                <h3 class="text-lg font-bold text-gray-900"><?php echo e($item->name); ?></h3>

                                <?php if($item->category): ?>
                                    <p class="text-sm text-gray-600 mt-1">Category: <?php echo e($item->category); ?></p>
                                <?php endif; ?>

                                <?php if($item->brand || $item->model): ?>
                                    <p class="text-sm text-gray-600 mt-1">
                                        <?php if($item->brand): ?>
                                            <?php echo e($item->brand); ?>

                                        <?php endif; ?>
                                        <?php if($item->model): ?>
                                            <?php echo e($item->brand ? ' - ' : ''); ?><?php echo e($item->model); ?>

                                        <?php endif; ?>
                                    </p>
                                <?php endif; ?>

                                <?php if($item->location): ?>
                                    <p class="text-sm text-gray-600 mt-1">Location: <?php echo e($item->location); ?></p>
                                <?php endif; ?>

                                <?php if($item->purchase_date): ?>
                                    <p class="text-sm text-gray-600 mt-1">Purchased: <?php echo e($item->purchase_date->format('M d, Y')); ?></p>
                                <?php endif; ?>

                                <?php if($item->purchase_price): ?>
                                    <p class="text-sm text-gray-600 mt-1">Price: Rp <?php echo e(number_format($item->purchase_price, 0, ',', '.')); ?></p>
                                <?php endif; ?>

                                <?php if($item->description): ?>
                                    <p class="text-sm text-gray-600 mt-3"><?php echo e(Str::limit($item->description, 100)); ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-between">
                                <a href="<?php echo e(route('gym.equipment.show', $item)); ?>" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">View Details</a>
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('gym.equipment.edit', $item)); ?>" class="text-yellow-600 hover:text-yellow-900 text-sm font-medium"><i class="fas fa-edit mr-2"></i>Edit</a>
                                    <!-- Search and Filters -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
        <i class="fas fa-filter mr-2 text-indigo-600"></i>
        Filter Gym Equipment
    </h3>
    <form action="<?php echo e(route('gym.equipment.destroy', $item)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this equipment?');">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium"><i class="fas fa-trash mr-2"></i>Delete</button>
                                    </form>
</div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="col-span-3 py-8 text-center text-gray-500">
                            <p>No equipment found.</p>
                            <a href="<?php echo e(route('gym.equipment.create')); ?>" class="mt-2 inline-block text-indigo-600 hover:text-indigo-900">Add your first equipment</a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    <?php echo e($equipment->withQueryString()->links()); ?>

                </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/equipment/index.blade.php ENDPATH**/ ?>