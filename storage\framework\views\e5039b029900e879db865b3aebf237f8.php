<?php $__env->startSection('page-title', 'Trainers'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Trainers</h2>
        <p class="text-gray-600">Manage your gym trainers and staff</p>
    </div>
    <a href="<?php echo e(route('gym.trainers.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Add Trainer
    </a>
</div>

<!-- Search and Filters -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
        <i class="fas fa-filter mr-2 text-indigo-600"></i>
        Filter Trainers
    </h3>
    <form action="<?php echo e(route('gym.trainers.index')); ?>" method="GET" class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search by name, email or phone"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <div class="w-full md:w-48">
            <select name="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Status</option>
                <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
            </select>
        </div>
        <div class="w-full md:w-48">
            <select name="specialization" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Specializations</option>
                <?php $__currentLoopData = $specializations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $specialization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($specialization); ?>" <?php echo e(request('specialization') == $specialization ? 'selected' : ''); ?>>
                        <?php echo e($specialization); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="w-full md:w-48">
            <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="created_at" <?php echo e(request('sort') == 'created_at' ? 'selected' : ''); ?>>Date Added</option>
                <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Name</option>
                <option value="hourly_rate" <?php echo e(request('sort') == 'hourly_rate' ? 'selected' : ''); ?>>Hourly Rate</option>
            </select>
        </div>
        <div class="w-full md:w-48">
            <select name="direction" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="desc" <?php echo e(request('direction') == 'desc' ? 'selected' : ''); ?>>Descending</option>
                <option value="asc" <?php echo e(request('direction') == 'asc' ? 'selected' : ''); ?>>Ascending</option>
            </select>
        </div>
        <div>
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
                <i class="fas fa-search mr-2"></i>
                Filter
            </button>
        </div>
    </form>
</div>

<!-- Trainers Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <?php $__empty_1 = true; $__currentLoopData = $trainers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trainer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
            <div class="p-6">
                <!-- Trainer Photo and Status -->
                <div class="flex flex-col items-center mb-4">
                    <?php if($trainer->photo): ?>
                        <div class="h-32 w-32 rounded-full overflow-hidden mb-3">
                            <img src="<?php echo e(Storage::url($trainer->photo)); ?>" alt="<?php echo e($trainer->name); ?>" class="h-32 w-32 object-cover"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="h-32 w-32 rounded-full bg-indigo-100 flex items-center justify-center" style="display: none;">
                                <span class="text-indigo-600 text-3xl font-medium"><?php echo e(substr($trainer->name, 0, 1)); ?></span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="h-32 w-32 rounded-full bg-indigo-100 flex items-center justify-center mb-3">
                            <span class="text-indigo-600 text-3xl font-medium"><?php echo e(substr($trainer->name, 0, 1)); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($trainer->is_active): ?>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    <?php else: ?>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    <?php endif; ?>
                </div>
                <h3 class="text-lg font-bold text-gray-900 text-center mb-3"><?php echo e($trainer->name); ?></h3>

                <div class="text-sm text-gray-600 text-center space-y-1">
                    <?php if($trainer->email): ?>
                        <p class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <?php echo e($trainer->email); ?>

                        </p>
                    <?php endif; ?>

                    <?php if($trainer->phone): ?>
                        <p class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <?php echo e($trainer->phone); ?>

                        </p>
                    <?php endif; ?>

                    <?php if($trainer->hourly_rate): ?>
                        <p class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Rp <?php echo e(number_format($trainer->hourly_rate, 0, ',', '.')); ?>/hour
                        </p>
                    <?php endif; ?>
                </div>

                <?php if($trainer->specializations && count($trainer->specializations) > 0): ?>
                    <div class="mt-3 text-center">
                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider">Specializations</h4>
                        <div class="mt-1 flex flex-wrap gap-1 justify-center">
                            <?php $__currentLoopData = $trainer->specializations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $specialization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800"><?php echo e($specialization); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($trainer->bio): ?>
                    <div class="mt-3">
                        <p class="text-sm text-gray-600 text-center"><?php echo e(Str::limit($trainer->bio, 100)); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="mt-4 pt-4 border-t border-gray-200 flex justify-center space-x-4">
                    <a href="<?php echo e(route('gym.trainers.show', $trainer)); ?>" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                        <i class="fas fa-eye mr-1"></i>View
                    </a>
                    <a href="<?php echo e(route('gym.trainers.edit', $trainer)); ?>" class="text-yellow-600 hover:text-yellow-900 text-sm font-medium">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </a>
                    <form action="<?php echo e(route('gym.trainers.destroy', $trainer)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this trainer?');">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                            <i class="fas fa-trash mr-1"></i>Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-span-3 py-8 text-center text-gray-500">
            <p>No trainers found.</p>
            <a href="<?php echo e(route('gym.trainers.create')); ?>" class="mt-2 inline-block text-indigo-600 hover:text-indigo-900">
                <i class="fas fa-plus mr-1"></i>Add your first trainer
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<div class="mt-6">
    <?php echo e($trainers->withQueryString()->links()); ?>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/trainers/index.blade.php ENDPATH**/ ?>