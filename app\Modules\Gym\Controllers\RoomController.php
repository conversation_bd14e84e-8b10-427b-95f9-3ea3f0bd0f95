<?php

namespace App\Modules\Gym\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gym\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoomController extends Controller
{
    /**
     * Display a listing of the rooms.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $tenantId = Auth::user()->tenant_id;
        
        $query = Room::where('tenant_id', $tenantId);

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('location', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('room_type')) {
            $query->where('room_type', $request->room_type);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('bookable')) {
            $query->where('is_bookable', $request->bookable === 'yes');
        }

        // Apply sorting
        $sortField = $request->get('sort', 'name');
        $sortDirection = $request->get('direction', 'asc');
        
        if (in_array($sortField, ['name', 'room_type', 'capacity', 'created_at'])) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('name', 'asc');
        }

        $rooms = $query->paginate(15)->appends($request->query());

        return view('gym::rooms.index', compact('rooms'));
    }

    /**
     * Show the form for creating a new room.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $roomTypes = Room::getRoomTypes();
        $amenityOptions = Room::getAmenityOptions();
        
        return view('gym::rooms.create', compact('roomTypes', 'amenityOptions'));
    }

    /**
     * Store a newly created room in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'capacity' => 'required|integer|min:1|max:1000',
            'room_type' => 'required|string|in:' . implode(',', array_keys(Room::getRoomTypes())),
            'location' => 'nullable|string|max:255',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|in:' . implode(',', array_keys(Room::getAmenityOptions())),
            'hourly_rate' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_bookable' => 'boolean',
            'booking_advance_days' => 'nullable|integer|min:1|max:365',
            'minimum_booking_duration' => 'nullable|integer|min:15|max:480',
            'maximum_booking_duration' => 'nullable|integer|min:30|max:1440',
            'operating_hours' => 'nullable|array',
            'notes' => 'nullable|string',
        ]);

        $tenantId = Auth::user()->tenant_id;

        $data = $request->all();
        $data['tenant_id'] = $tenantId;
        $data['created_by'] = Auth::id();
        $data['updated_by'] = Auth::id();

        // Handle amenities
        if (empty($data['amenities'])) {
            $data['amenities'] = [];
        }

        // Handle operating hours
        if (empty($data['operating_hours'])) {
            $data['operating_hours'] = [];
        }

        $room = Room::create($data);

        return redirect()->route('gym.rooms.index')
            ->with('success', 'Room created successfully.');
    }

    /**
     * Display the specified room.
     *
     * @param  \App\Modules\Gym\Models\Room  $room
     * @return \Illuminate\View\View
     */
    public function show(Room $room)
    {
        if ($room->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        // Get recent sessions for this room
        $recentSessions = $room->privateTrainingSessions()
            ->with(['member', 'trainer'])
            ->orderBy('scheduled_date', 'desc')
            ->orderBy('scheduled_start_time', 'desc')
            ->limit(10)
            ->get();

        // Get upcoming sessions
        $upcomingSessions = $room->privateTrainingSessions()
            ->with(['member', 'trainer'])
            ->where('scheduled_date', '>=', now()->toDateString())
            ->where('status', 'scheduled')
            ->orderBy('scheduled_date', 'asc')
            ->orderBy('scheduled_start_time', 'asc')
            ->limit(10)
            ->get();

        return view('gym::rooms.show', compact('room', 'recentSessions', 'upcomingSessions'));
    }

    /**
     * Show the form for editing the specified room.
     *
     * @param  \App\Modules\Gym\Models\Room  $room
     * @return \Illuminate\View\View
     */
    public function edit(Room $room)
    {
        if ($room->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        $roomTypes = Room::getRoomTypes();
        $amenityOptions = Room::getAmenityOptions();
        
        return view('gym::rooms.edit', compact('room', 'roomTypes', 'amenityOptions'));
    }

    /**
     * Update the specified room in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Modules\Gym\Models\Room  $room
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Room $room)
    {
        if ($room->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'capacity' => 'required|integer|min:1|max:1000',
            'room_type' => 'required|string|in:' . implode(',', array_keys(Room::getRoomTypes())),
            'location' => 'nullable|string|max:255',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|in:' . implode(',', array_keys(Room::getAmenityOptions())),
            'hourly_rate' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_bookable' => 'boolean',
            'booking_advance_days' => 'nullable|integer|min:1|max:365',
            'minimum_booking_duration' => 'nullable|integer|min:15|max:480',
            'maximum_booking_duration' => 'nullable|integer|min:30|max:1440',
            'operating_hours' => 'nullable|array',
            'notes' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['updated_by'] = Auth::id();

        // Handle amenities
        if (empty($data['amenities'])) {
            $data['amenities'] = [];
        }

        // Handle operating hours
        if (empty($data['operating_hours'])) {
            $data['operating_hours'] = [];
        }

        $room->update($data);

        return redirect()->route('gym.rooms.index')
            ->with('success', 'Room updated successfully.');
    }

    /**
     * Remove the specified room from storage.
     *
     * @param  \App\Modules\Gym\Models\Room  $room
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Room $room)
    {
        if ($room->tenant_id !== Auth::user()->tenant_id) {
            abort(403);
        }

        // Check if room has any scheduled sessions
        $scheduledSessions = $room->privateTrainingSessions()
            ->where('scheduled_date', '>=', now()->toDateString())
            ->where('status', 'scheduled')
            ->count();

        if ($scheduledSessions > 0) {
            return redirect()->route('gym.rooms.index')
                ->with('error', 'Cannot delete room with scheduled sessions. Please reschedule or cancel the sessions first.');
        }

        $room->delete();

        return redirect()->route('gym.rooms.index')
            ->with('success', 'Room deleted successfully.');
    }

    /**
     * Get available rooms for a specific date and time (API endpoint).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableRooms(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'room_type' => 'nullable|string',
        ]);

        $tenantId = Auth::user()->tenant_id;
        
        $query = Room::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->where('is_bookable', true);

        if ($request->filled('room_type')) {
            $query->where('room_type', $request->room_type);
        }

        $rooms = $query->get()->filter(function ($room) use ($request) {
            return $room->isAvailableAt(
                $request->date,
                $request->start_time,
                $request->end_time
            );
        });

        return response()->json([
            'rooms' => $rooms->map(function ($room) {
                return [
                    'id' => $room->id,
                    'name' => $room->name,
                    'room_type' => $room->room_type,
                    'capacity' => $room->capacity,
                    'hourly_rate' => $room->hourly_rate,
                    'location' => $room->location,
                ];
            })->values()
        ]);
    }
}
