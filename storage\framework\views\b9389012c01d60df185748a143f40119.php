<?php $__env->startSection('page-title', 'Subscriptions'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Subscriptions</h2>
        <p class="text-gray-600">Manage member subscriptions and payments</p>
    </div>
    <a href="<?php echo e(route('gym.subscriptions.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
        <i class="fas fa-plus mr-2"></i>
        Add Subscription
    </a>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input type="text" placeholder="Search subscriptions..." class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="expired">Expired</option>
                <option value="cancelled">Cancelled</option>
            </select>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Membership</label>
            <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <option value="">All Memberships</option>
                <?php $__currentLoopData = $memberships ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $membership): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($membership->id); ?>"><?php echo e($membership->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="flex items-end">
            <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg w-full">
                <i class="fas fa-search mr-2"></i>Filter
            </button>
        </div>
    </div>
</div>

<!-- Subscriptions Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-800">All Subscriptions</h3>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membership</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $subscriptions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full overflow-hidden">
                                    <?php if($subscription->member->photo): ?>
                                        <img src="<?php echo e(Storage::url($subscription->member->photo)); ?>" alt="<?php echo e($subscription->member->name); ?>" class="h-10 w-10 object-cover"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-medium" style="display: none;">
                                            <?php echo e(substr($subscription->member->name ?? 'M', 0, 1)); ?>

                                        </div>
                                    <?php else: ?>
                                        <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-medium">
                                            <?php echo e(substr($subscription->member->name ?? 'M', 0, 1)); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($subscription->member->name ?? 'Member Name'); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($subscription->member->member_code ?? 'M001'); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo e($subscription->membership->name ?? 'Membership Plan'); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($subscription->membership->duration_days ?? 30); ?> days</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <?php echo e(isset($subscription->start_date) ? $subscription->start_date->format('M d, Y') : 'Jan 01, 2024'); ?> -
                                <?php echo e(isset($subscription->end_date) ? $subscription->end_date->format('M d, Y') : 'Jan 31, 2024'); ?>

                            </div>
                            <div class="text-sm text-gray-500">
                                <?php if(isset($subscription->end_date)): ?>
                                    <?php if($subscription->end_date->isPast()): ?>
                                        Expired <?php echo e($subscription->end_date->diffForHumans()); ?>

                                    <?php else: ?>
                                        Expires <?php echo e($subscription->end_date->diffForHumans()); ?>

                                    <?php endif; ?>
                                <?php else: ?>
                                    Expires in 15 days
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">Rp <?php echo e(number_format($subscription->amount_paid ?? 150000, 0, ',', '.')); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($subscription->payment_method ?? 'Cash'); ?></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php
                                $status = $subscription->status ?? 'active';
                                $statusColors = [
                                    'active' => 'bg-green-100 text-green-800',
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'expired' => 'bg-red-100 text-red-800',
                                    'cancelled' => 'bg-gray-100 text-gray-800'
                                ];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo e($statusColors[$status] ?? 'bg-gray-100 text-gray-800'); ?>">
                                <?php echo e(ucfirst($status)); ?>

                            </span>
                            <?php if($subscription->auto_renew ?? false): ?>
                                <span class="ml-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Auto-renew
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('gym.subscriptions.show', $subscription->id ?? '#')); ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('gym.subscriptions.edit', $subscription->id ?? '#')); ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo e(route('gym.subscriptions.renew', $subscription->id ?? '#')); ?>" class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-redo"></i>
                                </a>
                                <button onclick="confirmDelete(<?php echo e($subscription->id ?? 0); ?>)" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-id-badge text-4xl mb-4"></i>
                            <p class="text-lg">No subscriptions found</p>
                            <p class="text-sm">Start by creating your first subscription</p>
                            <a href="<?php echo e(route('gym.subscriptions.create')); ?>" class="mt-4 inline-block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                                Create First Subscription
                            </a>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if(isset($subscriptions) && method_exists($subscriptions, 'links')): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <?php echo e($subscriptions->withQueryString()->links()); ?>

        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete(subscriptionId) {
    if (confirm('Are you sure you want to delete this subscription?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(route('gym.subscriptions.index')); ?>/${subscriptionId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '<?php echo e(csrf_token()); ?>';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/subscriptions/index.blade.php ENDPATH**/ ?>