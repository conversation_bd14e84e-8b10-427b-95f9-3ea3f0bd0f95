<?php

namespace App\Models;

use App\Modules\Tenant\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class MemberSubscription extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'member_subscriptions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'member_id',
        'subscribable_type', // Polymorphic type (e.g., App\Modules\Gym\Models\Membership)
        'subscribable_id',   // Polymorphic ID
        'module_type',       // gym, spa, restaurant, etc.
        'subscription_code',
        'start_date',
        'end_date',
        'status', // active, expired, cancelled, suspended
        'price',
        'payment_method',
        'payment_status', // pending, paid, partial, failed
        'auto_renewal',
        'renewal_date',
        'notes',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'renewal_date' => 'date',
        'price' => 'decimal:2',
        'auto_renewal' => 'boolean',
    ];

    /**
     * Get the tenant that owns the subscription.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the member that owns the subscription.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * Get the subscribable model (polymorphic).
     */
    public function subscribable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who created the subscription.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who last updated the subscription.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * Scope to filter by module type.
     */
    public function scopeForModule($query, string $module)
    {
        return $query->where('module_type', $module);
    }

    /**
     * Scope to filter active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope to filter expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    /**
     * Scope to filter by status.
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' 
            && $this->start_date <= now() 
            && $this->end_date >= now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->end_date < now();
    }

    /**
     * Check if subscription is about to expire.
     */
    public function isExpiringWithin(int $days = 7): bool
    {
        return $this->end_date <= now()->addDays($days);
    }

    /**
     * Get days remaining until expiration.
     */
    public function getDaysRemainingAttribute(): int
    {
        return max(0, now()->diffInDays($this->end_date, false));
    }

    /**
     * Get subscription status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'expired' => 'Expired',
            'cancelled' => 'Cancelled',
            'suspended' => 'Suspended',
            'pending' => 'Pending',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get module type display.
     */
    public function getModuleDisplayAttribute(): string
    {
        return match($this->module_type) {
            'gym' => 'Gym & Fitness',
            'spa' => 'Spa & Wellness',
            'restaurant' => 'Restaurant & Dining',
            'retail' => 'Retail & Shopping',
            'events' => 'Events & Bookings',
            default => ucfirst($this->module_type)
        };
    }

    /**
     * Extend subscription by days.
     */
    public function extend(int $days): bool
    {
        $this->end_date = $this->end_date->addDays($days);
        return $this->save();
    }

    /**
     * Cancel subscription.
     */
    public function cancel(string $reason = null): bool
    {
        $this->status = 'cancelled';
        if ($reason) {
            $this->notes = ($this->notes ? $this->notes . "\n" : '') . "Cancelled: " . $reason;
        }
        return $this->save();
    }

    /**
     * Suspend subscription.
     */
    public function suspend(string $reason = null): bool
    {
        $this->status = 'suspended';
        if ($reason) {
            $this->notes = ($this->notes ? $this->notes . "\n" : '') . "Suspended: " . $reason;
        }
        return $this->save();
    }

    /**
     * Reactivate subscription.
     */
    public function reactivate(): bool
    {
        $this->status = 'active';
        return $this->save();
    }

    /**
     * Generate unique subscription code.
     */
    public static function generateSubscriptionCode(int $tenantId, string $modulePrefix = 'SUB'): string
    {
        $count = static::where('tenant_id', $tenantId)->count() + 1;
        return $modulePrefix . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get available statuses.
     */
    public static function getStatuses(): array
    {
        return [
            'active' => 'Active',
            'expired' => 'Expired',
            'cancelled' => 'Cancelled',
            'suspended' => 'Suspended',
            'pending' => 'Pending',
        ];
    }

    /**
     * Get available module types.
     */
    public static function getModuleTypes(): array
    {
        return [
            'gym' => 'Gym & Fitness',
            'spa' => 'Spa & Wellness',
            'restaurant' => 'Restaurant & Dining',
            'retail' => 'Retail & Shopping',
            'events' => 'Events & Bookings',
        ];
    }
}
