<?php

use App\Modules\Gym\Controllers\GymController;
use App\Modules\Gym\Controllers\MemberController;
use App\Modules\Gym\Controllers\MemberAuthController;
use App\Modules\Gym\Controllers\MembershipController;
use App\Modules\Gym\Controllers\ClassController;
use App\Modules\Gym\Controllers\TrainerController;
use App\Modules\Gym\Controllers\AttendanceController;
use App\Modules\Gym\Controllers\EquipmentController;
use App\Modules\Gym\Controllers\EquipmentCategoryController;
use App\Modules\Gym\Controllers\SessionTypeController;
use App\Modules\Gym\Controllers\SubscriptionController;
use App\Modules\Gym\Controllers\ReportController;
use App\Modules\Gym\Controllers\PrivateTrainingPackageController;
use App\Modules\Gym\Controllers\PrivateTrainingSubscriptionController;
use App\Modules\Gym\Controllers\PrivateTrainingSessionController;
use App\Modules\Gym\Controllers\TrainerCommissionController;
use App\Modules\Gym\Controllers\ProductController;
use App\Modules\Gym\Controllers\ProductCategoryController;
use App\Modules\Gym\Controllers\StaffController;
use App\Modules\Gym\Controllers\RoomController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Gym Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Gym module.
|
*/

Route::middleware(['web', 'auth', 'module:gym'])->prefix('gym')->name('gym.')->group(function () {
    // Dashboard
    Route::get('/', [GymController::class, 'index'])->name('dashboard');

    // Members
    Route::resource('members', MemberController::class);
    Route::get('members/{member}/attendance', [MemberController::class, 'attendance'])->name('members.attendance');
    Route::get('members/{member}/subscriptions', [MemberController::class, 'subscriptions'])->name('members.subscriptions');
    Route::get('members/{member}/progress', [MemberController::class, 'progress'])->name('members.progress');

    // Memberships
    Route::resource('memberships', MembershipController::class);
    Route::patch('memberships/{membership}/toggle-status', [MembershipController::class, 'toggleStatus'])->name('memberships.toggle-status');

    // Classes
    Route::resource('classes', ClassController::class);
    Route::get('classes/{class}/attendance', [ClassController::class, 'attendance'])->name('classes.attendance');
    Route::post('classes/{class}/attendance', [ClassController::class, 'storeAttendance'])->name('classes.attendance.store');

    // Trainers
    Route::resource('trainers', TrainerController::class);
    Route::get('trainers/{trainer}/classes', [TrainerController::class, 'classes'])->name('trainers.classes');

    // Attendance
    Route::get('attendance', [AttendanceController::class, 'index'])->name('attendance.index');
    Route::get('attendance/create', [AttendanceController::class, 'create'])->name('attendance.create');
    Route::post('attendance', [AttendanceController::class, 'store'])->name('attendance.store');
    Route::get('attendance/{attendance}', [AttendanceController::class, 'show'])->name('attendance.show');
    Route::get('attendance/{attendance}/edit', [AttendanceController::class, 'edit'])->name('attendance.edit');
    Route::put('attendance/{attendance}', [AttendanceController::class, 'update'])->name('attendance.update');
    Route::patch('attendance/{attendance}', [AttendanceController::class, 'update'])->name('attendance.patch');
    Route::delete('attendance/{attendance}', [AttendanceController::class, 'destroy'])->name('attendance.destroy');

    // Equipment Categories
    Route::resource('equipment-categories', EquipmentCategoryController::class);

    // Equipment
    Route::resource('equipment', EquipmentController::class);
    Route::get('equipment/{equipment}/maintenance', [EquipmentController::class, 'maintenance'])->name('equipment.maintenance');
    Route::post('equipment/{equipment}/maintenance', [EquipmentController::class, 'storeMaintenance'])->name('equipment.maintenance.store');

    // Session Types
    Route::resource('session-types', SessionTypeController::class);

    // Subscriptions
    Route::resource('subscriptions', SubscriptionController::class);
    Route::get('subscriptions/{subscription}/renew', [SubscriptionController::class, 'renew'])->name('subscriptions.renew');
    Route::post('subscriptions/{subscription}/renew', [SubscriptionController::class, 'storeRenewal'])->name('subscriptions.renew.store');

    // Room Management
    Route::resource('rooms', RoomController::class);
    Route::get('rooms/available', [RoomController::class, 'getAvailableRooms'])->name('rooms.available');

    // Reports
    Route::get('reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('reports/members', [ReportController::class, 'members'])->name('reports.members');
    Route::get('reports/attendance', [ReportController::class, 'attendance'])->name('reports.attendance');
    Route::get('reports/revenue', [ReportController::class, 'revenue'])->name('reports.revenue');
    Route::get('reports/classes', [ReportController::class, 'classes'])->name('reports.classes');

    // Product Categories (must be before products resource route)
    Route::prefix('products/categories')->name('products.categories.')->group(function () {
        Route::get('/', [ProductCategoryController::class, 'index'])->name('index');
        Route::get('/create', [ProductCategoryController::class, 'create'])->name('create');
        Route::post('/', [ProductCategoryController::class, 'store'])->name('store');
        Route::get('/{category}', [ProductCategoryController::class, 'show'])->name('show');
        Route::get('/{category}/edit', [ProductCategoryController::class, 'edit'])->name('edit');
        Route::put('/{category}', [ProductCategoryController::class, 'update'])->name('update');
        Route::delete('/{category}', [ProductCategoryController::class, 'destroy'])->name('destroy');
    });

    // Products
    Route::resource('products', ProductController::class);
    Route::post('products/{product}/update-stock', [ProductController::class, 'updateStock'])->name('products.update-stock');

    // Staff Management
    Route::resource('staff', StaffController::class);

    // Private Training Packages (explicit routes)
    Route::get('private-training/packages', [PrivateTrainingPackageController::class, 'index'])->name('private-training.packages.index');
    Route::get('private-training/packages/create', [PrivateTrainingPackageController::class, 'create'])->name('private-training.packages.create');
    Route::post('private-training/packages', [PrivateTrainingPackageController::class, 'store'])->name('private-training.packages.store');
    Route::get('private-training/packages/{package}', [PrivateTrainingPackageController::class, 'show'])->name('private-training.packages.show');
    Route::get('private-training/packages/{package}/edit', [PrivateTrainingPackageController::class, 'edit'])->name('private-training.packages.edit');
    Route::put('private-training/packages/{package}', [PrivateTrainingPackageController::class, 'update'])->name('private-training.packages.update');
    Route::delete('private-training/packages/{package}', [PrivateTrainingPackageController::class, 'destroy'])->name('private-training.packages.destroy');

    // Test routes
    Route::get('private-training/packages-test', function() {
        return 'Packages route test works!';
    });
    Route::get('private-training/packages-controller-test', [PrivateTrainingPackageController::class, 'test']);

    // Private Training Main Routes
    Route::get('private-training', [PrivateTrainingSessionController::class, 'index'])->name('private-training.index');
    Route::get('private-training/create', [PrivateTrainingSessionController::class, 'createSession'])->name('private-training.create');
    Route::post('private-training', [PrivateTrainingSessionController::class, 'storeSession'])->name('private-training.store');
    Route::get('private-training/{session}', [PrivateTrainingSessionController::class, 'show'])->name('private-training.show');
    Route::get('private-training/{session}/edit', [PrivateTrainingSessionController::class, 'edit'])->name('private-training.edit');
    Route::put('private-training/{session}', [PrivateTrainingSessionController::class, 'update'])->name('private-training.update');
    Route::delete('private-training/{session}', [PrivateTrainingSessionController::class, 'destroy'])->name('private-training.destroy');

    Route::prefix('private-training/subscriptions')->name('private-training.subscriptions.')->group(function () {
        Route::get('/', [PrivateTrainingSubscriptionController::class, 'index'])->name('index');
        Route::get('/create', [PrivateTrainingSubscriptionController::class, 'create'])->name('create');
        Route::post('/', [PrivateTrainingSubscriptionController::class, 'store'])->name('store');
        Route::get('/{subscription}', [PrivateTrainingSubscriptionController::class, 'show'])->name('show');
        Route::get('/{subscription}/edit', [PrivateTrainingSubscriptionController::class, 'edit'])->name('edit');
        Route::put('/{subscription}', [PrivateTrainingSubscriptionController::class, 'update'])->name('update');
        Route::get('/{subscription}/reactivate', [PrivateTrainingSubscriptionController::class, 'reactivate'])->name('reactivate');
        Route::post('/{subscription}/reactivate', [PrivateTrainingSubscriptionController::class, 'processReactivation'])->name('process-reactivation');
    });

    Route::prefix('private-training/sessions')->name('private-training.sessions.')->group(function () {
        Route::get('/', [PrivateTrainingSessionController::class, 'index'])->name('index');
        Route::get('/subscription/{subscription}/create', [PrivateTrainingSessionController::class, 'create'])->name('create');
        Route::post('/subscription/{subscription}', [PrivateTrainingSessionController::class, 'store'])->name('store');
        Route::get('/{session}', [PrivateTrainingSessionController::class, 'show'])->name('show');
        Route::get('/{session}/edit', [PrivateTrainingSessionController::class, 'edit'])->name('edit');
        Route::put('/{session}', [PrivateTrainingSessionController::class, 'update'])->name('update');
        Route::post('/{session}/complete', [PrivateTrainingSessionController::class, 'markAsCompleted'])->name('complete');
        Route::post('/{session}/no-show', [PrivateTrainingSessionController::class, 'markAsNoShow'])->name('no-show');
        Route::post('/{session}/cancel', [PrivateTrainingSessionController::class, 'markAsCancelled'])->name('cancel');
        Route::get('/{session}/reschedule', [PrivateTrainingSessionController::class, 'rescheduleForm'])->name('reschedule-form');
        Route::post('/{session}/reschedule', [PrivateTrainingSessionController::class, 'reschedule'])->name('reschedule');
    });

    Route::prefix('private-training/commissions')->name('private-training.commissions.')->group(function () {
        Route::get('/', [TrainerCommissionController::class, 'index'])->name('index');
        Route::get('/create', [TrainerCommissionController::class, 'create'])->name('create');
        Route::get('/report', [TrainerCommissionController::class, 'report'])->name('report');
        Route::post('/', [TrainerCommissionController::class, 'store'])->name('store');
        Route::get('/{commission}', [TrainerCommissionController::class, 'show'])->name('show');
        Route::get('/{commission}/edit', [TrainerCommissionController::class, 'edit'])->name('edit');
        Route::put('/{commission}', [TrainerCommissionController::class, 'update'])->name('update');
        Route::post('/{commission}/mark-as-paid', [TrainerCommissionController::class, 'markAsPaid'])->name('mark-as-paid');
        Route::post('/{commission}/mark-as-cancelled', [TrainerCommissionController::class, 'markAsCancelled'])->name('mark-as-cancelled');
    });
});
