<?php

namespace App\Modules\Gym\Models;

use App\Modules\Tenant\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrivateTrainingSession extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'gym_private_training_sessions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'gym_private_training_subscription_id',
        'gym_private_training_package_id',
        'gym_member_id',
        'gym_trainer_id',
        'room_id',
        'scheduled_date',
        'scheduled_start_time',
        'scheduled_end_time',
        'actual_start_time',
        'actual_end_time',
        'status', // scheduled, completed, cancelled, no_show, rescheduled
        'location',
        'session_type',
        'goals',
        'notes',
        'feedback',
        'rating',
        'price',
        'payment_status',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'scheduled_date' => 'date',
        'scheduled_start_time' => 'datetime',
        'scheduled_end_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'rating' => 'integer',
        'price' => 'decimal:2',
    ];

    /**
     * Get the tenant that owns the session.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the subscription that owns the session.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(PrivateTrainingSubscription::class, 'gym_private_training_subscription_id');
    }

    /**
     * Get the member associated with the session.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'gym_member_id');
    }

    /**
     * Get the trainer associated with the session.
     */
    public function trainer(): BelongsTo
    {
        return $this->belongsTo(Trainer::class, 'gym_trainer_id');
    }

    /**
     * Get the package associated with the session (for standalone sessions).
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(PrivateTrainingPackage::class, 'gym_private_training_package_id');
    }

    /**
     * Get the room for the session.
     */
    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id');
    }

    /**
     * Mark the session as completed.
     *
     * @param string|null $actualStartTime
     * @param string|null $actualEndTime
     * @param string|null $notes
     * @return void
     */
    public function markAsCompleted(?string $actualStartTime = null, ?string $actualEndTime = null, ?string $notes = null): void
    {
        $this->status = 'completed';

        if ($actualStartTime) {
            $this->actual_start_time = $actualStartTime;
        }

        if ($actualEndTime) {
            $this->actual_end_time = $actualEndTime;
        }

        if ($notes) {
            $this->notes = $notes;
        }

        $this->save();

        // Reset missed sessions count on the subscription
        if ($this->subscription) {
            $this->subscription->resetMissedSessions();
        }
    }

    /**
     * Mark the session as no-show.
     *
     * @param string|null $notes
     * @return void
     */
    public function markAsNoShow(?string $notes = null): void
    {
        $this->status = 'no_show';

        if ($notes) {
            $this->notes = $notes;
        }

        $this->save();

        // Increment missed sessions count on the subscription
        if ($this->subscription) {
            $this->subscription->incrementMissedSessions();
        }
    }

    /**
     * Mark the session as cancelled.
     *
     * @param string|null $notes
     * @param bool $countAsMissed Whether to count this cancellation as a missed session
     * @return void
     */
    public function markAsCancelled(?string $notes = null, bool $countAsMissed = true): void
    {
        $this->status = 'cancelled';

        if ($notes) {
            $this->notes = $notes;
        }

        $this->save();

        // Increment missed sessions count on the subscription if needed
        if ($countAsMissed && $this->subscription) {
            $this->subscription->incrementMissedSessions();
        }
    }

    /**
     * Reschedule the session.
     *
     * @param string $newDate
     * @param string $newStartTime
     * @param string $newEndTime
     * @param string|null $notes
     * @return void
     */
    public function reschedule(string $newDate, string $newStartTime, string $newEndTime, ?string $notes = null): void
    {
        $this->status = 'rescheduled';

        if ($notes) {
            $this->notes = $notes;
        }

        $this->save();

        // Create a new session with the rescheduled details
        $newSession = $this->replicate();
        $newSession->status = 'scheduled';
        $newSession->scheduled_date = $newDate;
        $newSession->scheduled_start_time = $newStartTime;
        $newSession->scheduled_end_time = $newEndTime;
        $newSession->actual_start_time = null;
        $newSession->actual_end_time = null;
        $newSession->notes = "Rescheduled from session #" . $this->id;
        $newSession->feedback = null;
        $newSession->rating = null;
        $newSession->created_at = now();
        $newSession->updated_at = now();
        $newSession->save();
    }
}
