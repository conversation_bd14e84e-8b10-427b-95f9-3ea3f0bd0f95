<?php $__env->startSection('content'); ?>
<div class="bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Welcome Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Welcome back, <?php echo e(Auth::user()->name); ?>!</h1>
                    <p class="text-gray-600 mt-2">Here's what's happening with your business today.</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500"><?php echo e(now()->format('l, F j, Y')); ?></div>
                    <div class="text-lg font-semibold text-gray-800" id="current-time"><?php echo e(now()->format('g:i A')); ?></div>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>



        <!-- Dashboard Content -->
        <?php if($tenant): ?>
            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Sidebar -->
                <div class="lg:col-span-1 space-y-6">
                    <!-- Business Information Card -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-indigo-100 p-2 rounded-full mr-3">
                                <i class="fas fa-store text-indigo-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Business Info</h3>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-500">Store Name</p>
                                <p class="font-semibold text-gray-900"><?php echo e($tenant->name); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Business Type</p>
                                <p class="font-medium text-gray-700"><?php echo e($tenant->settings['business_type'] ?? 'Not specified'); ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Status</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active
                                </span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Member Since</p>
                                <p class="font-medium text-gray-700"><?php echo e($tenant->created_at->format('M Y')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Trial/Subscription Status Card -->
                    <?php if($tenant->isOnTrial()): ?>
                        <div class="bg-orange-50 border-2 border-orange-200 rounded-lg shadow p-6">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-clock text-xl mr-3 text-orange-600"></i>
                                <h3 class="text-lg font-bold text-orange-800">Trial Active</h3>
                            </div>
                            <div class="space-y-2">
                                <p class="text-gray-800">
                                    <span class="font-bold text-2xl text-orange-600"><?php echo e(intval($tenant->trialDaysRemaining())); ?></span>
                                    <span class="text-gray-700">days left</span>
                                </p>
                                <p class="text-sm text-gray-600">
                                    <?php if($tenant->getCurrentSubscription() && $tenant->getCurrentSubscription()->trial_ends_at): ?>
                                        Trial expires on <?php echo e($tenant->getCurrentSubscription()->trial_ends_at->format('M d, Y')); ?>

                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="mt-4">
                                <a href="/upgrade" class="block w-full text-center bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-rocket mr-2"></i>
                                    Upgrade Now
                                </a>
                            </div>
                        </div>
                    <?php elseif($tenant->getCurrentSubscription()): ?>
                        <div class="bg-green-50 border-2 border-green-200 rounded-lg shadow p-6">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-crown text-xl mr-3 text-green-600"></i>
                                <h3 class="text-lg font-bold text-green-800"><?php echo e($tenant->getCurrentSubscription()->plan_display_name); ?></h3>
                            </div>
                            <div class="space-y-2">
                                <p class="text-gray-800">
                                    <span class="font-bold text-xl text-green-600"><?php echo e(intval($tenant->getCurrentSubscription()->days_remaining)); ?></span>
                                    <span class="text-gray-700">days remaining</span>
                                </p>
                                <p class="text-sm text-gray-600">
                                    Renews on <?php echo e($tenant->getCurrentSubscription()->ends_at->format('M d, Y')); ?>

                                </p>
                            </div>
                            <?php if($tenant->isSubscriptionExpiringSoon()): ?>
                                <div class="mt-4">
                                    <a href="/upgrade" class="block w-full text-center bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                        <i class="fas fa-refresh mr-2"></i>
                                        Renew Now
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Module Access Card -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                <i class="fas fa-rocket text-blue-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Quick Access</h3>
                        </div>

                        <?php if($activeModules->count() > 0): ?>
                            <div class="space-y-2">
                                <?php if($activeModules->count() == 1): ?>
                                    <!-- Single Module - Direct Link -->
                                    <?php $module = $activeModules->first(); ?>
                                    <a href="/<?php echo e($module->availableModule->code); ?>"
                                       class="flex items-center justify-between p-3 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors group">
                                        <div class="flex items-center">
                                            <i class="fas fa-cube text-indigo-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900"><?php echo e($module->availableModule->name); ?></p>
                                                <?php if($module->is_primary): ?>
                                                    <p class="text-xs text-indigo-600 font-medium">Primary Module</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <i class="fas fa-arrow-right text-indigo-600 group-hover:translate-x-1 transition-transform"></i>
                                    </a>
                                <?php else: ?>
                                    <!-- Multiple Modules - Dropdown -->
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open"
                                                class="w-full flex items-center justify-between p-3 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors">
                                            <div class="flex items-center">
                                                <i class="fas fa-cubes text-indigo-600 mr-3"></i>
                                                <div class="text-left">
                                                    <p class="font-medium text-gray-900">Active Modules</p>
                                                    <p class="text-xs text-gray-500"><?php echo e($activeModules->count()); ?> modules available</p>
                                                </div>
                                            </div>
                                            <i class="fas fa-chevron-down text-indigo-600 transition-transform" :class="{ 'rotate-180': open }"></i>
                                        </button>

                                        <div x-show="open"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="opacity-0 transform scale-95"
                                             x-transition:enter-end="opacity-100 transform scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="opacity-100 transform scale-100"
                                             x-transition:leave-end="opacity-0 transform scale-95"
                                             class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                                            <div class="py-2">
                                                <?php $__currentLoopData = $activeModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="/<?php echo e($module->availableModule->code); ?>"
                                                       class="flex items-center justify-between px-4 py-3 hover:bg-gray-50 transition-colors group">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-cube text-gray-400 mr-3"></i>
                                                            <div>
                                                                <p class="font-medium text-gray-900"><?php echo e($module->availableModule->name); ?></p>
                                                                <?php if($module->is_primary): ?>
                                                                    <p class="text-xs text-indigo-600 font-medium">Primary</p>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <i class="fas fa-external-link-alt text-gray-400 group-hover:text-indigo-600 transition-colors"></i>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Upgrade Link -->
                                <a href="/upgrade"
                                   class="flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group mt-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-plus-circle text-green-600 mr-3"></i>
                                        <p class="font-medium text-gray-900">Add More Modules</p>
                                    </div>
                                    <i class="fas fa-arrow-right text-green-600 group-hover:translate-x-1 transition-transform"></i>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-exclamation-circle text-gray-400 text-2xl mb-2"></i>
                                <p class="text-gray-500 text-sm mb-3">No active modules</p>
                                <a href="/upgrade"
                                   class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i>
                                    Get Started
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="lg:col-span-3 space-y-6">
                    <!-- Module Status Summary -->
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Module Status</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-100">
                                <div class="flex items-center">
                                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                                        <i class="fas fa-bolt text-blue-500"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-blue-700">Active Modules</p>
                                        <p class="text-2xl font-bold text-blue-800"><?php echo e($activeModules->count()); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 p-4 rounded-lg border border-green-100">
                                <div class="flex items-center">
                                    <div class="bg-green-100 p-2 rounded-full mr-3">
                                        <i class="fas fa-check-circle text-green-500"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-green-700">Primary Module</p>
                                        <p class="text-xl font-bold text-green-800">
                                            <?php if($primaryModule): ?>
                                                <?php echo e($primaryModule->availableModule->name); ?>

                                            <?php else: ?>
                                                None
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                                <div class="flex items-center">
                                    <div class="bg-yellow-100 p-2 rounded-full mr-3">
                                        <i class="fas fa-clock text-yellow-500"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-yellow-700">Expiring Soon</p>
                                        <p class="text-2xl font-bold text-yellow-800">
                                            <?php echo e($activeModules->filter(function($m) { return $m->expires_at && $m->expires_at->diffInDays(now()) < 30; })->count()); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Your Active Modules -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Your Active Modules</h2>

                        <?php if($activeModules->count() > 0): ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php $__currentLoopData = $activeModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                        <div class="bg-indigo-600 text-white p-4">
                                            <div class="flex justify-between items-center">
                                                <h3 class="text-lg font-semibold"><?php echo e($module->availableModule->name); ?></h3>
                                                <?php if($module->is_primary): ?>
                                                    <span class="bg-white text-indigo-600 text-xs font-bold px-2 py-1 rounded">PRIMARY</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="p-4">
                                            <p class="text-gray-600 mb-4"><?php echo e($module->availableModule->description); ?></p>

                                            <div class="flex justify-between items-center mb-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Active
                                                </span>
                                                <?php if($module->expires_at): ?>
                                                    <div class="text-xs text-gray-500">
                                                        Expires: <?php echo e($module->expires_at->format('M d, Y')); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <a href="/<?php echo e($module->availableModule->code); ?>"
                                               class="inline-block w-full text-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                                                Access Module
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded">
                                <p>You don't have any active modules. Please contact your administrator.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded">
                    <p>You are not associated with any tenant. Please contact your administrator.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modals -->
<?php echo $__env->make('auth::partials.module-modals', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<!-- Alpine.js for Dropdown -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

<!-- Simple JavaScript for Clock -->
<script>
    // Real-time clock update
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        const clockElement = document.getElementById('current-time');
        if (clockElement) {
            clockElement.textContent = timeString;
        }
    }

    // Update clock every second
    setInterval(updateClock, 1000);

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('[x-data]');
        dropdowns.forEach(dropdown => {
            if (!dropdown.contains(event.target)) {
                // This will be handled by Alpine.js
            }
        });
    });
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Auth/Resources/views/dashboard.blade.php ENDPATH**/ ?>