<?php

use App\Modules\Gym\Controllers\MemberAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Member Routes (Public Access) - OLD VERSION
|--------------------------------------------------------------------------
|
| These routes are for gym members and can be accessed publicly.
| Members don't need admin authentication to access these routes.
|
*/

// Member Authentication Routes (Public Access)
Route::middleware(['web'])->prefix('member')->name('member.')->group(function () {
    // Welcome page (accessible to all)
    Route::get('/', function () {
        return view('gym::member-auth.welcome');
    })->name('welcome');

    // Guest routes (accessible only when not logged in as member)
    Route::middleware(['guest:member'])->group(function () {
        Route::get('login', [MemberAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [MemberAuthController::class, 'login']);
        Route::get('register', [MemberAuthController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [MemberAuthController::class, 'register']);
    });

    // Authenticated member routes
    Route::middleware(['auth:member'])->group(function () {
        Route::get('dashboard', [MemberAuthController::class, 'dashboard'])->name('dashboard');
        Route::get('profile', [MemberAuthController::class, 'profile'])->name('profile');
        Route::put('profile', [MemberAuthController::class, 'updateProfile'])->name('profile.update');
        Route::get('change-password', [MemberAuthController::class, 'showChangePasswordForm'])->name('change-password');
        Route::put('change-password', [MemberAuthController::class, 'updatePassword'])->name('change-password.update');
        Route::post('logout', [MemberAuthController::class, 'logout'])->name('logout');

        // Member-specific features
        Route::get('attendance', [MemberAuthController::class, 'attendance'])->name('attendance');
        Route::get('classes', [MemberAuthController::class, 'classes'])->name('classes');
        Route::get('private-training', [MemberAuthController::class, 'privateTraining'])->name('private-training');
        Route::get('subscriptions', [MemberAuthController::class, 'subscriptions'])->name('subscriptions');
    });
});

// Alternative routes with gym prefix for backward compatibility
Route::middleware(['web'])->prefix('gym/member')->name('gym.member.')->group(function () {
    // Guest routes (accessible only when not logged in as member)
    Route::middleware(['guest:member'])->group(function () {
        Route::get('login', [MemberAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [MemberAuthController::class, 'login']);
        Route::get('register', [MemberAuthController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [MemberAuthController::class, 'register']);
    });

    // Authenticated member routes
    Route::middleware(['auth:member'])->group(function () {
        Route::get('dashboard', [MemberAuthController::class, 'dashboard'])->name('dashboard');
        Route::get('profile', [MemberAuthController::class, 'profile'])->name('profile');
        Route::put('profile', [MemberAuthController::class, 'updateProfile'])->name('profile.update');
        Route::get('change-password', [MemberAuthController::class, 'showChangePasswordForm'])->name('change-password');
        Route::put('change-password', [MemberAuthController::class, 'updatePassword'])->name('change-password.update');
        Route::post('logout', [MemberAuthController::class, 'logout'])->name('logout');
    });
});
