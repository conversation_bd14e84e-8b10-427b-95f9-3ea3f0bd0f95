@extends('gym::layouts.app')

@section('page-title', 'Room Details')

@section('content')
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Page Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">{{ $room->name }}</h1>
                    <p class="text-gray-600 mt-2">{{ \App\Modules\Gym\Models\Room::getRoomTypes()[$room->room_type] ?? $room->room_type }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('gym.rooms.edit', $room) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Room
                    </a>
                    <a href="{{ route('gym.rooms.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Rooms
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Room Information -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Basic Information -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Room Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Room Name</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Room Type</label>
                            <p class="mt-1 text-sm text-gray-900">{{ \App\Modules\Gym\Models\Room::getRoomTypes()[$room->room_type] ?? $room->room_type }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Capacity</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->capacity }} people</p>
                        </div>
                        
                        @if($room->location)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Location</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->location }}</p>
                        </div>
                        @endif
                        
                        @if($room->hourly_rate)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Hourly Rate</label>
                            <p class="mt-1 text-sm text-gray-900">${{ number_format($room->hourly_rate, 2) }}/hour</p>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Status</label>
                            <div class="mt-1 flex space-x-2">
                                @if($room->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-times-circle mr-1"></i>Inactive
                                    </span>
                                @endif
                                
                                @if($room->is_bookable)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-calendar-check mr-1"></i>Bookable
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    @if($room->description)
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-500">Description</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $room->description }}</p>
                    </div>
                    @endif
                </div>

                <!-- Booking Settings -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Settings</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Advance Booking</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->booking_advance_days }} days</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Min Duration</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->minimum_booking_duration }} minutes</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Max Duration</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $room->maximum_booking_duration }} minutes</p>
                        </div>
                    </div>
                </div>

                <!-- Amenities -->
                @if(!empty($room->amenities))
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Amenities</h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        @foreach($room->amenities as $amenity)
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                <span class="text-sm text-gray-700">{{ \App\Modules\Gym\Models\Room::getAmenityOptions()[$amenity] ?? $amenity }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Recent Sessions -->
                @if($recentSessions->count() > 0)
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Sessions</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentSessions as $session)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $session->scheduled_date->format('M d, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $session->scheduled_start_time->format('H:i') }} - {{ $session->scheduled_end_time->format('H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $session->member->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $session->trainer->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                {{ $session->status === 'completed' ? 'bg-green-100 text-green-800' : 
                                                   ($session->status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                                                   ($session->status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')) }}">
                                                {{ ucfirst($session->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Availability Status</span>
                            <span class="text-sm font-medium 
                                {{ $room->availability_status === 'available' ? 'text-green-600' : 
                                   ($room->availability_status === 'occupied' ? 'text-red-600' : 'text-gray-600') }}">
                                {{ ucfirst(str_replace('_', ' ', $room->availability_status)) }}
                            </span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total Sessions</span>
                            <span class="text-sm font-medium text-gray-900">{{ $recentSessions->count() + $upcomingSessions->count() }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Upcoming Sessions</span>
                            <span class="text-sm font-medium text-gray-900">{{ $upcomingSessions->count() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Sessions -->
                @if($upcomingSessions->count() > 0)
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Upcoming Sessions</h3>
                    
                    <div class="space-y-3">
                        @foreach($upcomingSessions->take(5) as $session)
                            <div class="border border-gray-200 rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-900">{{ $session->scheduled_date->format('M d') }}</span>
                                    <span class="text-xs text-gray-500">{{ $session->scheduled_start_time->format('H:i') }}</span>
                                </div>
                                <div class="text-xs text-gray-600">
                                    {{ $session->member->name ?? 'N/A' }} with {{ $session->trainer->name ?? 'N/A' }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('gym.private-training.create') }}?room_id={{ $room->id }}" 
                           class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm text-center block">
                            <i class="fas fa-plus mr-2"></i>Schedule Session
                        </a>
                        
                        <a href="{{ route('gym.rooms.edit', $room) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm text-center block">
                            <i class="fas fa-edit mr-2"></i>Edit Room
                        </a>
                        
                        @if($upcomingSessions->count() === 0)
                        <form action="{{ route('gym.rooms.destroy', $room) }}" method="POST" class="w-full">
                            @csrf
                            @method('DELETE')
                            <button type="submit" onclick="return confirm('Are you sure you want to delete this room?')"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-trash mr-2"></i>Delete Room
                            </button>
                        </form>
                        @endif
                    </div>
                </div>

                @if($room->notes)
                <!-- Notes -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                    <p class="text-sm text-gray-600">{{ $room->notes }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
