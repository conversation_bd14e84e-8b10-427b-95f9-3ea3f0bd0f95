@extends('gym::layouts.app')

@section('page-title', 'Edit Private Training Session')

@section('content')
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Edit Private Training Session</h2>
        <p class="text-gray-600">Update session details for {{ $session->member->name ?? 'client' }}</p>
    </div>
    <div class="flex space-x-2">
        <a href="/gym/private-training/{{ $session->id ?? '#' }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-eye mr-2"></i>
            View Details
        </a>
        <a href="/gym/private-training" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Sessions
        </a>
    </div>
</div>

<!-- Session Form -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-800">Session Information</h3>
    </div>

    <form action="/gym/private-training/{{ $session->id ?? '#' }}" method="POST" class="p-6">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Client & Trainer Information -->
            <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-users mr-2 text-indigo-600"></i>
                        Client & Trainer
                    </h4>

                    <div class="space-y-4">
                        <div>
                            <label for="gym_member_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Client <span class="text-red-500">*</span>
                            </label>
                            <select name="gym_member_id" id="gym_member_id" required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Choose a client...</option>
                                @foreach($members ?? [] as $member)
                                    <option value="{{ $member->id }}" {{ ($session->gym_member_id ?? '') == $member->id ? 'selected' : '' }}>
                                        {{ $member->name }} ({{ $member->member_code }})
                                    </option>
                                @endforeach
                            </select>
                            @error('gym_member_id')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="gym_trainer_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Trainer <span class="text-red-500">*</span>
                            </label>
                            <select name="gym_trainer_id" id="gym_trainer_id" required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Choose a trainer...</option>
                                @foreach($trainers ?? [] as $trainer)
                                    <option value="{{ $trainer->id }}" {{ ($session->gym_trainer_id ?? '') == $trainer->id ? 'selected' : '' }}>
                                        {{ $trainer->name }} - {{ $trainer->specialization ?? 'Personal Trainer' }}
                                    </option>
                                @endforeach
                            </select>
                            @error('gym_trainer_id')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="package_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Training Package
                            </label>
                            <select name="package_id" id="package_id"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Choose a package...</option>
                                @php
                                    $selectedPackageId = null;

                                    // Get package ID from session (standalone) or subscription
                                    if ($session->gym_private_training_package_id) {
                                        $selectedPackageId = $session->gym_private_training_package_id;
                                    } elseif ($session->subscription && $session->subscription->gym_private_training_package_id) {
                                        $selectedPackageId = $session->subscription->gym_private_training_package_id;
                                    }

                                    // For old sessions without package_id, try to guess from price
                                    if (!$selectedPackageId && $session->price && count($packages ?? []) > 0) {
                                        foreach ($packages as $pkg) {
                                            $pricePerSession = round($pkg->price / $pkg->sessions_count);
                                            if (abs($pricePerSession - $session->price) <= 1000) { // tolerance 1000
                                                $selectedPackageId = $pkg->id;
                                                break;
                                            }
                                        }
                                    }
                                @endphp

                                @foreach($packages ?? [] as $package)
                                    <option value="{{ $package->id }}"
                                            data-price="{{ $package->price }}"
                                            data-sessions="{{ $package->sessions_count }}"
                                            {{ $selectedPackageId == $package->id ? 'selected' : '' }}>
                                        {{ $package->name }} - {{ $package->sessions_count }} Session{{ $package->sessions_count > 1 ? 's' : '' }} (Rp {{ number_format($package->price, 0, ',', '.') }})
                                    </option>
                                @endforeach
                            </select>
                            @error('package_id')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror

                            @if(!$selectedPackageId && $session->price)
                                <p class="text-amber-600 text-xs mt-1">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This session was created before package tracking. Current price: Rp {{ number_format($session->price, 0, ',', '.') }}
                                </p>
                            @endif
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Session Status</label>
                            <select name="status" id="status"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="scheduled" {{ ($session->status ?? 'scheduled') == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                <option value="completed" {{ ($session->status ?? '') == 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ ($session->status ?? '') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                <option value="no_show" {{ ($session->status ?? '') == 'no_show' ? 'selected' : '' }}>No Show</option>
                            </select>
                            @error('status')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Information -->
            <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-calendar-alt mr-2 text-indigo-600"></i>
                        Schedule Details
                    </h4>

                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">
                                    Date <span class="text-red-500">*</span>
                                </label>
                                <input type="date" name="scheduled_date" id="scheduled_date" required
                                    value="{{ $session->scheduled_date ? $session->scheduled_date->format('Y-m-d') : '' }}"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                @error('scheduled_date')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="scheduled_start_time" class="block text-sm font-medium text-gray-700 mb-2">
                                    Start Time <span class="text-red-500">*</span>
                                </label>
                                <input type="time" name="scheduled_start_time" id="scheduled_start_time" required
                                    value="{{ $session->scheduled_start_time ? $session->scheduled_start_time->format('H:i') : '' }}"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                @error('scheduled_start_time')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="scheduled_end_time" class="block text-sm font-medium text-gray-700 mb-2">
                                End Time <span class="text-red-500">*</span>
                            </label>
                            <input type="time" name="scheduled_end_time" id="scheduled_end_time" required
                                value="{{ $session->scheduled_end_time ? $session->scheduled_end_time->format('H:i') : '' }}"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            @error('scheduled_end_time')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="room_id" class="block text-sm font-medium text-gray-700 mb-2">Room/Location</label>
                            <select name="room_id" id="room_id"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Select room...</option>
                                @foreach($rooms as $room)
                                    <option value="{{ $room->id }}" {{ ($session->room_id ?? old('room_id')) == $room->id ? 'selected' : '' }}>
                                        {{ $room->name }}
                                        @if($room->location) - {{ $room->location }} @endif
                                        ({{ \App\Modules\Gym\Models\Room::getRoomTypes()[$room->room_type] ?? $room->room_type }})
                                        @if($room->hourly_rate) - ${{ number_format($room->hourly_rate, 2) }}/hr @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('room_id')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 mt-1">Select a room for the training session. <a href="{{ route('gym.rooms.index') }}" class="text-indigo-600 hover:text-indigo-800">Manage rooms</a></p>
                        </div>

                        <div>
                            <label for="session_type" class="block text-sm font-medium text-gray-700 mb-2">Session Type</label>
                            <select name="session_type" id="session_type"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="strength_training" {{ ($session->session_type ?? '') == 'strength_training' ? 'selected' : '' }}>Strength Training</option>
                                <option value="cardio" {{ ($session->session_type ?? '') == 'cardio' ? 'selected' : '' }}>Cardio Training</option>
                                <option value="weight_loss" {{ ($session->session_type ?? '') == 'weight_loss' ? 'selected' : '' }}>Weight Loss</option>
                                <option value="muscle_building" {{ ($session->session_type ?? '') == 'muscle_building' ? 'selected' : '' }}>Muscle Building</option>
                                <option value="rehabilitation" {{ ($session->session_type ?? '') == 'rehabilitation' ? 'selected' : '' }}>Rehabilitation</option>
                                <option value="nutrition_consultation" {{ ($session->session_type ?? '') == 'nutrition_consultation' ? 'selected' : '' }}>Nutrition Consultation</option>
                                <option value="general_fitness" {{ ($session->session_type ?? '') == 'general_fitness' ? 'selected' : '' }}>General Fitness</option>
                            </select>
                            @error('session_type')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="mt-8">
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-indigo-600"></i>
                    Additional Information
                </h4>

                <div class="space-y-4">
                    <div>
                        <label for="goals" class="block text-sm font-medium text-gray-700 mb-2">Training Goals</label>
                        <textarea name="goals" id="goals" rows="3"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            placeholder="Describe the client's training goals and objectives...">{{ $session->goals ?? '' }}</textarea>
                        @error('goals')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Session Notes</label>
                        <textarea name="notes" id="notes" rows="3"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            placeholder="Any special instructions or notes for this session...">{{ $session->notes ?? '' }}</textarea>
                        @error('notes')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Session Price (Rp)</label>
                            <input type="number" name="price" id="price" min="0" step="1000"
                                value="{{ $session->price ?? '' }}"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                placeholder="150000">
                            @error('price')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
                            <select name="payment_status" id="payment_status"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="pending" {{ ($session->payment_status ?? 'pending') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="paid" {{ ($session->payment_status ?? '') == 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="partial" {{ ($session->payment_status ?? '') == 'partial' ? 'selected' : '' }}>Partial Payment</option>
                            </select>
                            @error('payment_status')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
            <a href="/gym/private-training" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg">
                Cancel
            </a>
            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg flex items-center">
                <i class="fas fa-save mr-2"></i>
                Update Session
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
// Auto-populate price based on package selection
document.getElementById('package_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const priceField = document.getElementById('price');

    if (selectedOption.value && selectedOption.dataset.price) {
        // Calculate price per session
        const totalPrice = parseFloat(selectedOption.dataset.price);
        const sessionsCount = parseInt(selectedOption.dataset.sessions);
        const pricePerSession = Math.round(totalPrice / sessionsCount);

        priceField.value = pricePerSession;
    } else {
        priceField.value = '';
    }
});
</script>
@endpush
@endsection
