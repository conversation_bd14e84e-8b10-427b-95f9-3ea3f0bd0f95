<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>404 - Page Not Found | BusinessPOS</title>
    <meta name="description" content="The page you're looking for doesn't exist. Return to BusinessPOS homepage or explore our features.">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Montserrat+Alternates:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <?php if(file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot'))): ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php else: ?>
        <style>
            @import url('https://cdn.tailwindcss.com');
        </style>
    <?php endif; ?>

    <style>
        body {
            font-family: 'Montserrat', sans-serif;
        }
        
        /* Headings menggunakan Montserrat Alternates */
        h1, h2, h3, h4, h5, h6, .heading-font {
            font-family: 'Montserrat Alternates', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-animation-delay {
            animation: float 6s ease-in-out infinite;
            animation-delay: -3s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .bounce-in {
            animation: bounceIn 1s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                transform: scale(0.3);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .slide-up {
            animation: slideUp 0.8s ease-out;
        }
        
        @keyframes slideUp {
            0% {
                transform: translateY(50px);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-indigo-100 rounded-full opacity-20 floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-100 rounded-full opacity-20 floating-animation-delay"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-yellow-100 rounded-full opacity-30 pulse-animation"></div>
        <div class="absolute bottom-1/4 right-1/4 w-24 h-24 bg-green-100 rounded-full opacity-25 floating-animation"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- 404 Number -->
        <div class="bounce-in mb-8">
            <h1 class="text-9xl md:text-[12rem] font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 leading-none">
                404
            </h1>
        </div>

        <!-- Error Message -->
        <div class="slide-up mb-8">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Oops! Page Not Found
            </h2>
            <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                The page you're looking for seems to have taken a coffee break. Don't worry, even the best websites need a moment to recharge!
            </p>
        </div>

        <!-- Illustration -->
        <div class="slide-up mb-12">
            <div class="relative inline-block">
                <div class="w-64 h-64 md:w-80 md:h-80 mx-auto bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center">
                    <div class="floating-animation">
                        <i class="fas fa-search text-6xl md:text-7xl text-indigo-600"></i>
                    </div>
                </div>
                <!-- Floating Elements -->
                <div class="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full floating-animation"></div>
                <div class="absolute -bottom-4 -left-4 w-6 h-6 bg-green-400 rounded-full floating-animation-delay"></div>
                <div class="absolute top-1/4 -left-8 w-4 h-4 bg-red-400 rounded-full pulse-animation"></div>
                <div class="absolute bottom-1/4 -right-8 w-5 h-5 bg-blue-400 rounded-full floating-animation"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="slide-up space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <a href="/" class="inline-flex items-center justify-center px-8 py-4 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <i class="fas fa-home mr-2"></i>
                Back to Homepage
            </a>
            <a href="#" onclick="history.back()" class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-50 text-gray-700 font-semibold rounded-lg border border-gray-300 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-left mr-2"></i>
                Go Back
            </a>
        </div>

        <!-- Help Section -->
        <div class="slide-up mt-16 p-6 bg-white rounded-xl shadow-lg max-w-2xl mx-auto">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                What can you do?
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <span>Check the URL spelling</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <span>Visit our homepage</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <span>Contact our support</span>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="slide-up mt-12">
            <h4 class="text-lg font-semibold text-gray-900 mb-6">Popular Pages</h4>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="/#features" class="px-4 py-2 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded-lg transition-colors">
                    Features
                </a>
                <a href="/#pricing" class="px-4 py-2 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg transition-colors">
                    Pricing
                </a>
                <a href="/login" class="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors">
                    Login
                </a>
                <a href="/register" class="px-4 py-2 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded-lg transition-colors">
                    Sign Up
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="slide-up mt-16 pt-8 border-t border-gray-200">
            <p class="text-gray-500 text-sm">
                © <?php echo e(date('Y')); ?> BusinessPOS. All rights reserved. | 
                <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-700 transition-colors">
                    Need help? Contact support
                </a>
            </p>
        </div>
    </div>

    <!-- JavaScript for interactions -->
    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effect to floating elements
            const floatingElements = document.querySelectorAll('.floating-animation, .floating-animation-delay');
            floatingElements.forEach(element => {
                element.addEventListener('click', function() {
                    this.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\resources\views/errors/404.blade.php ENDPATH**/ ?>