@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Edit Report</h3>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">Back to List</a>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('reports.update', $report->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="name" class="form-label">Report Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $report->name) }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="type" class="form-label">Report Type</label>
                            <select class="form-control" id="type" name="type" required>
                                <option value="">Select Report Type</option>
                                <option value="sales" {{ old('type', $report->type) == 'sales' ? 'selected' : '' }}>Sales Report</option>
                                <option value="inventory" {{ old('type', $report->type) == 'inventory' ? 'selected' : '' }}>Inventory Report</option>
                                <option value="customer" {{ old('type', $report->type) == 'customer' ? 'selected' : '' }}>Customer Report</option>
                                <option value="financial" {{ old('type', $report->type) == 'financial' ? 'selected' : '' }}>Financial Report</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $report->description) }}</textarea>
                        </div>

                        <div class="mb-3" id="parameters-container">
                            <label class="form-label">Parameters</label>
                            <div class="parameters-list">
                                <!-- Parameters will be dynamically added here based on report type -->
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Update Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
const parameterTemplates = {
    sales: [
        { name: 'date_range', type: 'daterange', label: 'Date Range' },
        { name: 'product_category', type: 'select', label: 'Product Category' }
    ],
    inventory: [
        { name: 'warehouse', type: 'select', label: 'Warehouse' },
        { name: 'stock_status', type: 'select', label: 'Stock Status' }
    ],
    customer: [
        { name: 'customer_group', type: 'select', label: 'Customer Group' },
        { name: 'activity_period', type: 'select', label: 'Activity Period' }
    ],
    financial: [
        { name: 'period', type: 'select', label: 'Period' },
        { name: 'report_type', type: 'select', label: 'Report Type' }
    ]
};

const savedParameters = @json($report->parameters ?? []);

function updateParameters() {
    const type = document.getElementById('type').value;
    const container = document.querySelector('.parameters-list');
    container.innerHTML = '';

    if (type && parameterTemplates[type]) {
        parameterTemplates[type].forEach(param => {
            const div = document.createElement('div');
            div.className = 'mb-3';
            const savedValue = savedParameters[param.name] || '';

            div.innerHTML = `
                <label class="form-label">${param.label}</label>
                ${param.type === 'daterange'
                    ? `<input type="text" class="form-control daterangepicker" name="parameters[${param.name}]" value="${savedValue}">`
                    : `<select class="form-control" name="parameters[${param.name}]">
                        <option value="">Select ${param.label}</option>
                       </select>`}
            `;
            container.appendChild(div);
        });

        // Initialize date range picker if needed
        const dateRanges = document.querySelectorAll('.daterangepicker');
        dateRanges.forEach(input => {
            // Add your date range picker initialization here
        });
    }
}

document.getElementById('type').addEventListener('change', updateParameters);
// Initialize parameters on page load
updateParameters();
</script>
@endpush
@endsection
