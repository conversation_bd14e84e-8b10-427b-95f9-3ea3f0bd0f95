<?php

namespace App\Modules\Gym\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Gym\Models\Room;
use App\Modules\Tenant\Models\Tenant;

class RoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all tenants that have gym module
        $tenants = Tenant::whereHas('modules', function ($query) {
            $query->where('name', 'gym');
        })->get();

        foreach ($tenants as $tenant) {
            $this->createRoomsForTenant($tenant->id);
        }
    }

    /**
     * Create sample rooms for a tenant.
     */
    private function createRoomsForTenant(int $tenantId): void
    {
        $rooms = [
            [
                'name' => 'Private Training Room 1',
                'description' => 'Spacious private room equipped with modern training equipment for one-on-one sessions.',
                'capacity' => 2,
                'room_type' => 'private_training',
                'location' => 'Ground Floor, East Wing',
                'amenities' => ['air_conditioning', 'mirrors', 'sound_system', 'mats', 'water_dispenser'],
                'hourly_rate' => 50.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 30,
                'minimum_booking_duration' => 60,
                'maximum_booking_duration' => 180,
                'operating_hours' => [
                    'monday' => ['open' => '06:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '06:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '06:00', 'close' => '22:00'],
                    'thursday' => ['open' => '06:00', 'close' => '22:00'],
                    'friday' => ['open' => '06:00', 'close' => '22:00'],
                    'saturday' => ['open' => '08:00', 'close' => '20:00'],
                    'sunday' => ['open' => '08:00', 'close' => '18:00'],
                ],
                'notes' => 'Premium private training room with state-of-the-art equipment.',
            ],
            [
                'name' => 'Private Training Room 2',
                'description' => 'Intimate training space perfect for personal training and small group sessions.',
                'capacity' => 3,
                'room_type' => 'private_training',
                'location' => 'Ground Floor, West Wing',
                'amenities' => ['air_conditioning', 'mirrors', 'mats', 'first_aid'],
                'hourly_rate' => 40.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 30,
                'minimum_booking_duration' => 60,
                'maximum_booking_duration' => 180,
                'operating_hours' => [
                    'monday' => ['open' => '06:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '06:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '06:00', 'close' => '22:00'],
                    'thursday' => ['open' => '06:00', 'close' => '22:00'],
                    'friday' => ['open' => '06:00', 'close' => '22:00'],
                    'saturday' => ['open' => '08:00', 'close' => '20:00'],
                    'sunday' => ['open' => '08:00', 'close' => '18:00'],
                ],
                'notes' => 'Cozy private training room suitable for various training styles.',
            ],
            [
                'name' => 'Group Class Studio A',
                'description' => 'Large studio space for group fitness classes and workshops.',
                'capacity' => 25,
                'room_type' => 'group_class',
                'location' => 'Second Floor, North Wing',
                'amenities' => ['air_conditioning', 'mirrors', 'sound_system', 'mats', 'projector', 'wifi'],
                'hourly_rate' => 80.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 60,
                'minimum_booking_duration' => 45,
                'maximum_booking_duration' => 120,
                'operating_hours' => [
                    'monday' => ['open' => '05:30', 'close' => '23:00'],
                    'tuesday' => ['open' => '05:30', 'close' => '23:00'],
                    'wednesday' => ['open' => '05:30', 'close' => '23:00'],
                    'thursday' => ['open' => '05:30', 'close' => '23:00'],
                    'friday' => ['open' => '05:30', 'close' => '23:00'],
                    'saturday' => ['open' => '07:00', 'close' => '21:00'],
                    'sunday' => ['open' => '07:00', 'close' => '20:00'],
                ],
                'notes' => 'Main group class studio with excellent acoustics and lighting.',
            ],
            [
                'name' => 'Yoga & Pilates Studio',
                'description' => 'Serene studio designed specifically for yoga, pilates, and meditation classes.',
                'capacity' => 20,
                'room_type' => 'yoga',
                'location' => 'Second Floor, South Wing',
                'amenities' => ['air_conditioning', 'mirrors', 'sound_system', 'mats', 'towels'],
                'hourly_rate' => 60.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 45,
                'minimum_booking_duration' => 60,
                'maximum_booking_duration' => 90,
                'operating_hours' => [
                    'monday' => ['open' => '06:00', 'close' => '21:00'],
                    'tuesday' => ['open' => '06:00', 'close' => '21:00'],
                    'wednesday' => ['open' => '06:00', 'close' => '21:00'],
                    'thursday' => ['open' => '06:00', 'close' => '21:00'],
                    'friday' => ['open' => '06:00', 'close' => '21:00'],
                    'saturday' => ['open' => '08:00', 'close' => '19:00'],
                    'sunday' => ['open' => '08:00', 'close' => '18:00'],
                ],
                'notes' => 'Peaceful environment with natural lighting and calming atmosphere.',
            ],
            [
                'name' => 'Functional Training Area',
                'description' => 'Open space for functional training, CrossFit-style workouts, and circuit training.',
                'capacity' => 15,
                'room_type' => 'functional',
                'location' => 'Ground Floor, Center',
                'amenities' => ['air_conditioning', 'sound_system', 'first_aid', 'emergency_button'],
                'hourly_rate' => 70.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 30,
                'minimum_booking_duration' => 45,
                'maximum_booking_duration' => 120,
                'operating_hours' => [
                    'monday' => ['open' => '05:00', 'close' => '23:00'],
                    'tuesday' => ['open' => '05:00', 'close' => '23:00'],
                    'wednesday' => ['open' => '05:00', 'close' => '23:00'],
                    'thursday' => ['open' => '05:00', 'close' => '23:00'],
                    'friday' => ['open' => '05:00', 'close' => '23:00'],
                    'saturday' => ['open' => '07:00', 'close' => '22:00'],
                    'sunday' => ['open' => '07:00', 'close' => '21:00'],
                ],
                'notes' => 'High-intensity training area with specialized equipment.',
            ],
            [
                'name' => 'Recovery & Stretching Room',
                'description' => 'Quiet space dedicated to recovery, stretching, and rehabilitation exercises.',
                'capacity' => 8,
                'room_type' => 'recovery',
                'location' => 'Ground Floor, Quiet Zone',
                'amenities' => ['air_conditioning', 'mirrors', 'mats', 'first_aid'],
                'hourly_rate' => 30.00,
                'is_active' => true,
                'is_bookable' => true,
                'booking_advance_days' => 14,
                'minimum_booking_duration' => 30,
                'maximum_booking_duration' => 90,
                'operating_hours' => [
                    'monday' => ['open' => '06:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '06:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '06:00', 'close' => '22:00'],
                    'thursday' => ['open' => '06:00', 'close' => '22:00'],
                    'friday' => ['open' => '06:00', 'close' => '22:00'],
                    'saturday' => ['open' => '08:00', 'close' => '20:00'],
                    'sunday' => ['open' => '08:00', 'close' => '18:00'],
                ],
                'notes' => 'Peaceful environment for recovery and therapeutic exercises.',
            ],
        ];

        foreach ($rooms as $roomData) {
            $roomData['tenant_id'] = $tenantId;
            $roomData['created_by'] = 1; // Assuming admin user ID is 1
            $roomData['updated_by'] = 1;

            Room::create($roomData);
        }
    }
}
