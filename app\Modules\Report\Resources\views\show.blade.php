@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">{{ $report->name }}</h3>
                    <div>
                        <a href="{{ route('reports.edit', $report->id) }}" class="btn btn-primary">Edit Report</a>
                        <a href="{{ route('reports.index') }}" class="btn btn-secondary">Back to List</a>
                    </div>
                </div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="bg-light" style="width: 150px;">Report Type</th>
                                    <td>{{ ucfirst($report->type) }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Description</th>
                                    <td>{{ $report->description ?: 'No description provided' }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Last Generated</th>
                                    <td>{{ $report->last_generated_at ? $report->last_generated_at->format('Y-m-d H:i:s') : 'Never' }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Created</th>
                                    <td>{{ $report->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Last Updated</th>
                                    <td>{{ $report->updated_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Report Parameters</h5>
                                </div>
                                <div class="card-body">
                                    @if(!empty($report->parameters))
                                        <table class="table table-bordered">
                                            @foreach($report->parameters as $key => $value)
                                                <tr>
                                                    <th class="bg-light" style="width: 150px;">{{ ucwords(str_replace('_', ' ', $key)) }}</th>
                                                    <td>{{ is_array($value) ? implode(', ', $value) : $value }}</td>
                                                </tr>
                                            @endforeach
                                        </table>
                                    @else
                                        <p class="text-muted mb-0">No parameters configured</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4>Generate Report</h4>
                        <form action="{{ route('reports.generate', $report->id) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-file-export"></i> Generate Now
                            </button>
                        </form>

                        @if($report->last_generated_at)
                            <a href="{{ route('reports.download', $report->id) }}" class="btn btn-info">
                                <i class="fas fa-download"></i> Download Last Report
                            </a>
                        @endif
                    </div>

                    @if(!empty($reportData))
                        <div class="mt-4">
                            <h4>Preview</h4>
                            <div class="table-responsive">
                                <!-- Report preview content will be dynamically loaded here -->
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
