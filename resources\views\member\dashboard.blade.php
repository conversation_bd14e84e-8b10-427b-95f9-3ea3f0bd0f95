<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - {{ ucfirst($module ?? 'gym') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    @php
        $moduleConfig = [
            'gym' => ['icon' => 'fa-dumbbell', 'color' => 'from-blue-500 to-blue-600', 'name' => 'Gym & Fitness'],
            'spa' => ['icon' => 'fa-spa', 'color' => 'from-purple-500 to-purple-600', 'name' => 'Spa & Wellness'],
            'restaurant' => ['icon' => 'fa-utensils', 'color' => 'from-green-500 to-green-600', 'name' => 'Restaurant & Dining'],
            'retail' => ['icon' => 'fa-shopping-bag', 'color' => 'from-yellow-500 to-yellow-600', 'name' => 'Retail & Shopping'],
            'events' => ['icon' => 'fa-calendar-alt', 'color' => 'from-red-500 to-red-600', 'name' => 'Events & Bookings'],
        ];
        $config = $moduleConfig[$module ?? 'gym'] ?? $moduleConfig['gym'];
    @endphp

    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r {{ $config['color'] }} rounded-lg flex items-center justify-center text-white mr-3">
                        <i class="fas {{ $config['icon'] }}"></i>
                    </div>
                    <h1 class="text-xl font-bold text-gray-900">{{ $config['name'] }}</h1>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Module Switcher -->
                    <div class="relative">
                        <button id="moduleDropdown" class="flex items-center text-sm text-gray-700 hover:text-gray-900">
                            <i class="fas fa-th-large mr-2"></i>
                            Switch Module
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="moduleMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-10">
                            @foreach($moduleConfig as $key => $mod)
                                @if($key !== ($module ?? 'gym'))
                                    <form method="POST" action="{{ route('member.switch-module') }}">
                                        @csrf
                                        <input type="hidden" name="module" value="{{ $key }}">
                                        <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                            <div class="w-6 h-6 bg-gradient-to-r {{ $mod['color'] }} rounded flex items-center justify-center text-white mr-3">
                                                <i class="fas {{ $mod['icon'] }} text-xs"></i>
                                            </div>
                                            {{ $mod['name'] }}
                                        </button>
                                    </form>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-700">{{ $member->name ?? 'Member' }}</span>
                        <form method="POST" action="{{ route('member.logout') }}">
                            @csrf
                            <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                                <i class="fas fa-sign-out-alt mr-1"></i>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Welcome Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Welcome back, {{ $member->name ?? 'Member' }}!</h2>
                    <p class="text-gray-600 mt-2">Access your {{ $config['name'] }} services and manage your account</p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-r {{ $config['color'] }} rounded-full flex items-center justify-center text-white">
                    <i class="fas {{ $config['icon'] }} text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Module-specific Content -->
        @if($module === 'gym')
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white mr-4">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">My Classes</h3>
                            <p class="text-sm text-gray-600">View and book classes</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center text-white mr-4">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Personal Training</h3>
                            <p class="text-sm text-gray-600">Book PT sessions</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white mr-4">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Progress</h3>
                            <p class="text-sm text-gray-600">Track your fitness</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Member Info -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Member Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Member Code</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $member->member_code ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $member->email ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Phone</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $member->phone ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Member Type</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $member->member_type_display ?? 'N/A' }}</p>
                </div>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-700 mb-3">Module Access</h4>
                <div class="flex flex-wrap gap-2">
                    @if($member->modules ?? [])
                        @foreach($member->modules as $mod)
                            @if(isset($moduleConfig[$mod]))
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r {{ $moduleConfig[$mod]['color'] }} text-white">
                                    <i class="fas {{ $moduleConfig[$mod]['icon'] }} mr-1"></i>
                                    {{ $moduleConfig[$mod]['name'] }}
                                </span>
                            @endif
                        @endforeach
                    @else
                        <span class="text-sm text-gray-500">No module access configured</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        // Module dropdown toggle
        document.getElementById('moduleDropdown').addEventListener('click', function() {
            document.getElementById('moduleMenu').classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('moduleDropdown');
            const menu = document.getElementById('moduleMenu');
            if (!dropdown.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
