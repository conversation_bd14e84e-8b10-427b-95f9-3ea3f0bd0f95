<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('gym_rooms')) {
            Schema::create('gym_rooms', function (Blueprint $table) {
                $table->id();
                $table->foreignId('tenant_id')->constrained('tenants')->cascadeOnDelete();
                $table->string('name');
                $table->text('description')->nullable();
                $table->integer('capacity')->default(1);
                $table->enum('room_type', [
                    'private_training',
                    'group_class',
                    'cardio',
                    'strength',
                    'functional',
                    'yoga',
                    'spinning',
                    'boxing',
                    'dance',
                    'recovery',
                    'locker',
                    'office',
                    'storage',
                    'other'
                ])->default('private_training');
                $table->string('location')->nullable();
                $table->json('amenities')->nullable();
                $table->decimal('hourly_rate', 10, 2)->nullable();
                $table->boolean('is_active')->default(true);
                $table->boolean('is_bookable')->default(true);
                $table->integer('booking_advance_days')->default(30);
                $table->integer('minimum_booking_duration')->default(60); // in minutes
                $table->integer('maximum_booking_duration')->default(240); // in minutes
                $table->json('operating_hours')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
                $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
                $table->timestamps();

                // Indexes
                $table->index(['tenant_id', 'is_active']);
                $table->index(['tenant_id', 'room_type']);
                $table->index(['tenant_id', 'is_bookable']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gym_rooms');
    }
};
