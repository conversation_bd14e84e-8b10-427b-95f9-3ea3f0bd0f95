@extends('gym::layouts.app')

@section('page-title', 'Create New Class')

@section('content')
<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- <PERSON> Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create New Class</h1>
                    <p class="text-gray-600 mt-2">Create a new fitness class for your gym members</p>
                </div>
                <a href="{{ route('gym.classes.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Classes
                </a>
            </div>
        </div>

        <!-- Class Form -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">

            <form action="{{ route('gym.classes.store') }}" method="POST">
                @csrf

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="space-y-6">
                        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-2xl border border-indigo-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-indigo-500 to-indigo-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-info-circle text-sm"></i>
                                </div>
                                Basic Information
                            </h3>

                            <div class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Class Name <span class="text-red-500">*</span></label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea name="description" id="description" rows="3"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                        placeholder="Describe the class and its benefits...">{{ old('description') }}</textarea>
                                    @error('description')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="gym_trainer_id" class="block text-sm font-medium text-gray-700 mb-2">Trainer</label>
                                    <select name="gym_trainer_id" id="gym_trainer_id"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        <option value="">Select trainer...</option>
                                        @foreach($trainers as $trainer)
                                            <option value="{{ $trainer->id }}" {{ old('gym_trainer_id', $selectedTrainerId ?? '') == $trainer->id ? 'selected' : '' }}>
                                                {{ $trainer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('gym_trainer_id')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input type="text" name="location" id="location" value="{{ old('location') }}"
                                        placeholder="e.g., Studio A, Main Floor"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('location')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">Capacity <span class="text-red-500">*</span></label>
                                        <input type="number" name="capacity" id="capacity" value="{{ old('capacity', 10) }}" min="1" required
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('capacity')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price (Rp)</label>
                                        <input type="number" name="price" id="price" value="{{ old('price') }}" min="0" step="1000"
                                            placeholder="0"
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('price')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <div class="flex items-center p-4 bg-white rounded-lg border border-gray-200">
                                    <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <label for="is_active" class="ml-3 text-sm text-gray-700 font-medium">Active Class</label>
                                    @error('is_active')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Schedule Information -->
                    <div class="space-y-6">
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-100">
                            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white mr-3">
                                    <i class="fas fa-calendar-alt text-sm"></i>
                                </div>
                                Schedule Information
                            </h3>

                            <div class="space-y-4">
                                <div>
                                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">Duration (minutes) <span class="text-red-500">*</span></label>
                                    <input type="number" name="duration_minutes" id="duration_minutes" value="{{ old('duration_minutes', 60) }}" min="5" required
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    @error('duration_minutes')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">Start Time <span class="text-red-500">*</span></label>
                                        <input type="time" name="start_time" id="start_time" value="{{ old('start_time') }}" required
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('start_time')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">End Time <span class="text-red-500">*</span></label>
                                        <input type="time" name="end_time" id="end_time" value="{{ old('end_time') }}" required
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('end_time')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-3">Days of Week <span class="text-red-500">*</span></label>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="1" {{ in_array(1, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Monday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="2" {{ in_array(2, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Tuesday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="3" {{ in_array(3, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Wednesday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="4" {{ in_array(4, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Thursday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="5" {{ in_array(5, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Friday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="6" {{ in_array(6, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Saturday</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors">
                                            <input type="checkbox" name="days_of_week[]" value="7" {{ in_array(7, old('days_of_week', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-3 text-sm text-gray-700 font-medium">Sunday</span>
                                        </div>
                                    </div>
                                    @error('days_of_week')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date <span class="text-red-500">*</span></label>
                                        <input type="date" name="start_date" id="start_date" value="{{ old('start_date', now()->format('Y-m-d')) }}" required
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('start_date')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                        <input type="date" name="end_date" id="end_date" value="{{ old('end_date') }}"
                                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                        @error('end_date')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle text-blue-400"></i>
                                        </div>
                                        <div class="ml-3">
                                            <h4 class="text-sm font-medium text-blue-800">Class Schedule Tips</h4>
                                            <div class="mt-2 text-sm text-blue-700">
                                                <ul class="list-disc list-inside space-y-1">
                                                    <li>Leave end date empty for ongoing classes</li>
                                                    <li>End time will be calculated automatically based on duration</li>
                                                    <li>Select multiple days for recurring classes</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <a href="{{ route('gym.classes.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Create Class
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');
        const durationInput = document.getElementById('duration_minutes');

        // Function to update end time based on start time and duration
        function updateEndTime() {
            if (startTimeInput.value && durationInput.value) {
                const startTime = new Date(`2000-01-01T${startTimeInput.value}`);
                const durationMinutes = parseInt(durationInput.value);

                if (!isNaN(startTime.getTime()) && !isNaN(durationMinutes)) {
                    const endTime = new Date(startTime.getTime() + durationMinutes * 60000);

                    // Format time as HH:MM
                    const hours = String(endTime.getHours()).padStart(2, '0');
                    const minutes = String(endTime.getMinutes()).padStart(2, '0');
                    endTimeInput.value = `${hours}:${minutes}`;
                }
            }
        }

        // Add event listeners
        startTimeInput.addEventListener('change', updateEndTime);
        durationInput.addEventListener('change', updateEndTime);
        durationInput.addEventListener('input', updateEndTime);

        // Initial update
        if (startTimeInput.value && !endTimeInput.value) {
            updateEndTime();
        }
    });
</script>
@endpush
@endsection
