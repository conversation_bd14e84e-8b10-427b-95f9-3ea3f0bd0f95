<?php $__env->startSection('page-title', 'Session Types'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">Session Types</h2>
            <p class="text-gray-600">Manage training session types and categories</p>
        </div>
        <a href="<?php echo e(route('gym.session-types.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus mr-2"></i>
            Add Session Type
        </a>
    </div>

    <!-- Success Message -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex">
                <div class="py-1">
                    <i class="fas fa-check-circle mr-2"></i>
                </div>
                <div>
                    <p class="font-bold">Success!</p>
                    <p class="text-sm"><?php echo e(session('success')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex">
                <div class="py-1">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                </div>
                <div>
                    <p class="font-bold">Error!</p>
                    <p class="text-sm"><?php echo e(session('error')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Session Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $sessionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sessionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200">
                <div class="p-6">
                    <!-- Session Type Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl" style="background-color: <?php echo e($sessionType->color); ?>">
                                <i class="<?php echo e($sessionType->icon); ?>"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-semibold text-gray-900"><?php echo e($sessionType->name); ?></h3>
                                <p class="text-sm text-gray-500">
                                    <?php if($sessionType->duration_minutes): ?>
                                        <?php echo e($sessionType->formatted_duration); ?>

                                    <?php else: ?>
                                        Custom duration
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <?php if($sessionType->is_active): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Description -->
                    <?php if($sessionType->description): ?>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($sessionType->description); ?></p>
                    <?php endif; ?>

                    <!-- Usage Stats -->
                    <div class="bg-gray-50 rounded-lg p-3 mb-4">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-lg font-semibold text-gray-900"><?php echo e($sessionType->private_training_sessions_count ?? 0); ?></div>
                                <div class="text-xs text-gray-500">Private Sessions</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-gray-900"><?php echo e($sessionType->classes_count ?? 0); ?></div>
                                <div class="text-xs text-gray-500">Classes</div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('gym.session-types.show', $sessionType)); ?>" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i>View
                            </a>
                            <a href="<?php echo e(route('gym.session-types.edit', $sessionType)); ?>" class="text-yellow-600 hover:text-yellow-900 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </a>
                        </div>
                        <form action="<?php echo e(route('gym.session-types.destroy', $sessionType)); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" onclick="return confirm('Are you sure you want to delete this session type?')" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full">
                <div class="text-center py-12">
                    <div class="w-24 h-24 mx-auto mb-4 text-gray-400">
                        <i class="fas fa-clock text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No session types found</h3>
                    <p class="text-gray-500 mb-6">Get started by creating your first session type.</p>
                    <a href="<?php echo e(route('gym.session-types.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        Add Session Type
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if($sessionTypes->hasPages()): ?>
        <div class="mt-8">
            <?php echo e($sessionTypes->links()); ?>

        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/session-types/index.blade.php ENDPATH**/ ?>