<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Gym\Models\SessionType;
use App\Modules\Tenant\Models\Tenant;

class EnhancedGymSessionTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenants = Tenant::all();

        foreach ($tenants as $tenant) {
            $this->seedSessionTypesForTenant($tenant->id);
        }
    }

    private function seedSessionTypesForTenant(int $tenantId): void
    {
        $sessionTypes = [
            // Fitness Training
            [
                'name' => 'Strength Training',
                'slug' => 'strength-training',
                'category' => 'fitness',
                'description' => 'Build muscle strength and power with resistance training exercises',
                'duration_minutes' => 60,
                'default_price' => 150000,
                'color' => '#ef4444',
                'icon' => 'fas fa-dumbbell',
                'features' => ['Resistance exercises', 'Progressive overload', 'Form correction', 'Strength assessment'],
                'benefits' => ['Increased muscle mass', 'Better bone density', 'Improved metabolism', 'Enhanced functional strength'],
                'target_audience' => ['Beginners', 'Intermediate', 'Advanced athletes'],
                'equipment_needed' => ['Dumbbells', 'Barbells', 'Resistance bands', 'Weight machines'],
                'preparation_notes' => 'Wear comfortable workout clothes. Bring water bottle. Avoid heavy meals 2 hours before session.',
                'post_session_notes' => 'Stay hydrated. Get adequate rest. Follow recommended nutrition plan.',
                'is_popular' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Cardio Training',
                'slug' => 'cardio-training',
                'category' => 'fitness',
                'description' => 'Improve cardiovascular endurance and burn calories effectively',
                'duration_minutes' => 45,
                'default_price' => 120000,
                'color' => '#10b981',
                'icon' => 'fas fa-heartbeat',
                'features' => ['Heart rate monitoring', 'Interval training', 'Endurance building', 'Fat burning'],
                'benefits' => ['Better heart health', 'Weight loss', 'Increased stamina', 'Improved circulation'],
                'target_audience' => ['All fitness levels', 'Weight loss goals', 'Endurance athletes'],
                'equipment_needed' => ['Treadmill', 'Stationary bike', 'Rowing machine', 'Jump rope'],
                'preparation_notes' => 'Light meal 1-2 hours before. Bring towel and water. Wear proper athletic shoes.',
                'post_session_notes' => 'Cool down properly. Stretch major muscle groups. Monitor heart rate recovery.',
                'is_popular' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'HIIT Training',
                'slug' => 'hiit-training',
                'category' => 'fitness',
                'description' => 'High-Intensity Interval Training for maximum results in minimum time',
                'duration_minutes' => 30,
                'default_price' => 130000,
                'color' => '#f59e0b',
                'icon' => 'fas fa-fire',
                'features' => ['High-intensity intervals', 'Active recovery', 'Metabolic conditioning', 'Time-efficient'],
                'benefits' => ['Rapid fat loss', 'Improved VO2 max', 'Time-efficient', 'Afterburn effect'],
                'target_audience' => ['Intermediate', 'Advanced', 'Busy professionals'],
                'equipment_needed' => ['Kettlebells', 'Battle ropes', 'Plyometric boxes', 'Medicine balls'],
                'preparation_notes' => 'Good fitness base required. Avoid if injured. Bring extra water.',
                'post_session_notes' => 'Extended cool down needed. Monitor recovery. Rest 24-48 hours between sessions.',
                'is_popular' => true,
                'sort_order' => 3,
            ],

            // Nutrition & Diet
            [
                'name' => 'Nutrition Consultation',
                'slug' => 'nutrition-consultation',
                'category' => 'nutrition',
                'description' => 'Personalized nutrition guidance and comprehensive meal planning',
                'duration_minutes' => 60,
                'default_price' => 200000,
                'color' => '#8b5cf6',
                'icon' => 'fas fa-apple-alt',
                'features' => ['Dietary assessment', 'Meal planning', 'Supplement advice', 'Lifestyle coaching'],
                'benefits' => ['Better nutrition habits', 'Weight management', 'Improved energy', 'Health optimization'],
                'target_audience' => ['All levels', 'Weight management', 'Health conditions', 'Athletes'],
                'equipment_needed' => ['Body composition analyzer', 'Food scale', 'Measuring tools'],
                'preparation_notes' => 'Bring recent blood work if available. Food diary for past week. List current supplements.',
                'post_session_notes' => 'Follow meal plan gradually. Track progress weekly. Schedule follow-up in 4 weeks.',
                'is_popular' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Meal Prep Coaching',
                'slug' => 'meal-prep-coaching',
                'category' => 'nutrition',
                'description' => 'Learn to prepare healthy meals efficiently for the week',
                'duration_minutes' => 90,
                'default_price' => 250000,
                'color' => '#06b6d4',
                'icon' => 'fas fa-utensils',
                'features' => ['Meal prep techniques', 'Recipe development', 'Shopping guidance', 'Storage tips'],
                'benefits' => ['Time savings', 'Consistent nutrition', 'Cost effective', 'Healthy habits'],
                'target_audience' => ['Busy professionals', 'Families', 'Meal prep beginners'],
                'equipment_needed' => ['Kitchen tools', 'Storage containers', 'Food scale'],
                'preparation_notes' => 'Bring notebook for recipes. Clear kitchen schedule. List dietary preferences/restrictions.',
                'post_session_notes' => 'Start with 2-3 recipes. Prep on same day weekly. Adjust portions as needed.',
                'sort_order' => 5,
            ],

            // Wellness & Recovery
            [
                'name' => 'Flexibility & Mobility',
                'slug' => 'flexibility-mobility',
                'category' => 'wellness',
                'description' => 'Improve flexibility, mobility, and overall movement quality',
                'duration_minutes' => 45,
                'default_price' => 110000,
                'color' => '#84cc16',
                'icon' => 'fas fa-leaf',
                'features' => ['Stretching routines', 'Mobility exercises', 'Movement assessment', 'Injury prevention'],
                'benefits' => ['Better flexibility', 'Reduced stiffness', 'Injury prevention', 'Better posture'],
                'target_audience' => ['All ages', 'Office workers', 'Athletes', 'Seniors'],
                'equipment_needed' => ['Yoga mats', 'Foam rollers', 'Resistance bands', 'Mobility tools'],
                'preparation_notes' => 'Wear flexible clothing. Avoid stretching cold muscles. Mention any pain areas.',
                'post_session_notes' => 'Practice daily routine. Hold stretches 30+ seconds. Progress gradually.',
                'sort_order' => 6,
            ],

            // Assessment & Testing
            [
                'name' => 'Fitness Assessment',
                'slug' => 'fitness-assessment',
                'category' => 'assessment',
                'description' => 'Comprehensive fitness evaluation and personalized goal setting',
                'duration_minutes' => 90,
                'default_price' => 300000,
                'color' => '#dc2626',
                'icon' => 'fas fa-chart-line',
                'features' => ['Body composition', 'Strength testing', 'Cardio assessment', 'Goal setting'],
                'benefits' => ['Baseline measurements', 'Personalized programs', 'Progress tracking', 'Goal clarity'],
                'target_audience' => ['New members', 'Goal setters', 'Progress trackers'],
                'equipment_needed' => ['Body composition analyzer', 'Fitness testing equipment', 'Measurement tools'],
                'preparation_notes' => 'Fast 3 hours before. Wear minimal clothing. Bring health history. Avoid intense exercise 24h prior.',
                'post_session_notes' => 'Review results in detail. Set SMART goals. Schedule program design session.',
                'is_popular' => true,
                'sort_order' => 7,
            ],

            // Specialized Training
            [
                'name' => 'Sports-Specific Training',
                'slug' => 'sports-specific-training',
                'category' => 'specialized',
                'description' => 'Training tailored for specific sports performance enhancement',
                'duration_minutes' => 75,
                'default_price' => 180000,
                'color' => '#f97316',
                'icon' => 'fas fa-trophy',
                'features' => ['Sport-specific movements', 'Performance enhancement', 'Skill development', 'Competition prep'],
                'benefits' => ['Better performance', 'Injury prevention', 'Skill improvement', 'Competitive edge'],
                'target_audience' => ['Athletes', 'Sports enthusiasts', 'Competitive players'],
                'equipment_needed' => ['Sport-specific equipment', 'Agility tools', 'Performance monitors'],
                'preparation_notes' => 'Specify sport and position. Bring sport equipment if needed. Mention competition schedule.',
                'post_session_notes' => 'Practice skills regularly. Focus on weak areas. Monitor performance metrics.',
                'sort_order' => 8,
            ],
            [
                'name' => 'Rehabilitation Training',
                'slug' => 'rehabilitation-training',
                'category' => 'rehabilitation',
                'description' => 'Therapeutic exercise for injury recovery and pain management',
                'duration_minutes' => 60,
                'default_price' => 220000,
                'color' => '#059669',
                'icon' => 'fas fa-medkit',
                'features' => ['Therapeutic exercises', 'Pain management', 'Movement restoration', 'Injury prevention'],
                'benefits' => ['Faster recovery', 'Pain reduction', 'Restored function', 'Injury prevention'],
                'target_audience' => ['Injury recovery', 'Chronic pain', 'Post-surgery', 'Movement disorders'],
                'equipment_needed' => ['Therapeutic tools', 'Resistance bands', 'Balance equipment'],
                'requirements' => ['Medical clearance', 'Injury assessment', 'Doctor referral'],
                'preparation_notes' => 'Bring medical clearance. List all medications. Describe pain levels and triggers.',
                'post_session_notes' => 'Follow exercise protocol strictly. Ice if swelling. Report any pain increase.',
                'sort_order' => 9,
            ],

            // Wellness & Recovery
            [
                'name' => 'Recovery & Stress Relief',
                'slug' => 'recovery-stress-relief',
                'category' => 'wellness',
                'description' => 'Active recovery and stress relief techniques for mental wellness',
                'duration_minutes' => 60,
                'default_price' => 140000,
                'color' => '#6366f1',
                'icon' => 'fas fa-spa',
                'features' => ['Gentle movement', 'Breathing exercises', 'Stress relief', 'Sleep optimization'],
                'benefits' => ['Faster recovery', 'Stress reduction', 'Better sleep', 'Mental wellness'],
                'target_audience' => ['Stressed individuals', 'Overtraining', 'Insomnia', 'High performers'],
                'equipment_needed' => ['Massage tools', 'Aromatherapy', 'Relaxation music', 'Yoga props'],
                'preparation_notes' => 'Wear comfortable clothes. Avoid caffeine 4h before. Bring stress level assessment.',
                'post_session_notes' => 'Practice breathing techniques daily. Maintain sleep schedule. Reduce stressors.',
                'sort_order' => 10,
            ],
        ];

        foreach ($sessionTypes as $sessionType) {
            $sessionType['tenant_id'] = $tenantId;
            $sessionType['created_by'] = 1; // Admin user
            $sessionType['updated_by'] = 1;

            // Make slug unique across all tenants
            $baseSlug = $sessionType['slug'];
            $slug = $baseSlug . '-t' . $tenantId; // Add tenant ID to make it unique
            $counter = 1;

            while (SessionType::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-t' . $tenantId . '-' . $counter;
                $counter++;
            }

            $sessionType['slug'] = $slug;

            // Check if session type already exists for this tenant
            $existing = SessionType::where('tenant_id', $tenantId)
                ->where('name', $sessionType['name'])
                ->first();

            if ($existing) {
                // Update existing session type
                $existing->update($sessionType);
            } else {
                // Create new session type
                SessionType::create($sessionType);
            }
        }
    }
}
