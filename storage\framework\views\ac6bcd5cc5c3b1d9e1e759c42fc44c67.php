<?php $__env->startSection('page-title', 'Private Training'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Private Training</h2>
        <p class="text-gray-600">Manage private training sessions and packages</p>
    </div>
    <div class="flex items-center space-x-4">
        <!-- View Toggle -->
        <div class="bg-gray-100 rounded-lg p-1 flex">
            <button id="listViewBtn" class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 bg-white text-gray-900 shadow-sm">
                <i class="fas fa-list mr-2"></i>List
            </button>
            <button id="calendarViewBtn" class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 text-gray-600 hover:text-gray-900">
                <i class="fas fa-calendar mr-2"></i>Calendar
            </button>
        </div>
        <a href="/gym/private-training/create" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus mr-2"></i>
            Schedule Session
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-500 text-white mr-4">
                <i class="fas fa-user-friends"></i>
            </div>
            <div>
                <p class="text-sm text-blue-800 font-medium">Total Sessions</p>
                <p class="text-2xl font-bold text-blue-900"><?php echo e($sessions->count() ?? 0); ?></p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-500 text-white mr-4">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div>
                <p class="text-sm text-green-800 font-medium">Today's Sessions</p>
                <p class="text-2xl font-bold text-green-900">8</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-500 text-white mr-4">
                <i class="fas fa-users"></i>
            </div>
            <div>
                <p class="text-sm text-purple-800 font-medium">Active Clients</p>
                <p class="text-2xl font-bold text-purple-900">45</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-500 text-white mr-4">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div>
                <p class="text-sm text-yellow-800 font-medium">Monthly Revenue</p>
                <p class="text-2xl font-bold text-yellow-900">Rp 15M</p>
            </div>
        </div>
    </div>
</div>

<!-- List View Container -->
<div id="listView">
    <!-- Filter and Search -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" placeholder="Search sessions..." class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Trainer</label>
                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Trainers</option>
                    <option value="1">John Doe</option>
                    <option value="2">Jane Smith</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Status</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="no_show">No Show</option>
                </select>
            </div>
            <div class="flex items-end">
                <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg w-full">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </div>
    </div>

    <!-- Sessions Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-800">Private Training Sessions</h3>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainer</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $sessions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-medium">
                                    <?php echo e(substr($session->member->name ?? 'M', 0, 1)); ?>

                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($session->member->name ?? 'Member Name'); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($session->member->phone ?? '+62 123 456 789'); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white font-medium text-xs">
                                    <?php echo e(substr($session->trainer->name ?? 'T', 0, 1)); ?>

                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($session->trainer->name ?? 'Trainer Name'); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($session->trainer->specialization ?? 'Personal Trainer'); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <?php echo e(isset($session->scheduled_at) ? $session->scheduled_at->format('M d, Y') : 'Jan 15, 2024'); ?>

                            </div>
                            <div class="text-sm text-gray-500">
                                <?php echo e(isset($session->scheduled_at) ? $session->scheduled_at->format('H:i') : '10:00'); ?> -
                                <?php echo e(isset($session->end_time) ? $session->end_time->format('H:i') : '11:00'); ?>

                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900"><?php echo e($session->package->name ?? 'Personal Training'); ?></div>
                            <div class="text-sm text-gray-500">
                                <?php echo e($session->sessions_remaining ?? 5); ?>/<?php echo e($session->package->total_sessions ?? 10); ?> sessions left
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php
                                $status = $session->status ?? 'scheduled';
                                $statusColors = [
                                    'scheduled' => 'bg-blue-100 text-blue-800',
                                    'completed' => 'bg-green-100 text-green-800',
                                    'cancelled' => 'bg-red-100 text-red-800',
                                    'no_show' => 'bg-yellow-100 text-yellow-800'
                                ];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo e($statusColors[$status] ?? 'bg-gray-100 text-gray-800'); ?>">
                                <?php echo e(ucfirst($status)); ?>

                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="/gym/private-training/<?php echo e($session->id ?? '#'); ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/gym/private-training/<?php echo e($session->id ?? '#'); ?>/edit" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if($status === 'scheduled'): ?>
                                    <button onclick="markCompleted(<?php echo e($session->id ?? 0); ?>)" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button onclick="markCancelled(<?php echo e($session->id ?? 0); ?>)" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-times"></i>
                                    </button>
                                <?php endif; ?>
                                <button onclick="confirmDelete(<?php echo e($session->id ?? 0); ?>)" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-user-friends text-4xl mb-4"></i>
                            <p class="text-lg">No private training sessions found</p>
                            <p class="text-sm">Schedule your first private training session</p>
                            <a href="/gym/private-training/create" class="mt-4 inline-block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                                Schedule First Session
                            </a>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if(isset($sessions) && method_exists($sessions, 'links')): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <?php echo e($sessions->links()); ?>

        </div>
    <?php endif; ?>
    </div>
</div>

<!-- Calendar View Container -->
<div id="calendarView" class="hidden">
    <!-- Calendar Header -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <button id="prevMonth" class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <h3 id="currentMonth" class="text-xl font-semibold text-gray-800">January 2024</h3>
                <button id="nextMonth" class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="flex items-center space-x-2">
                <button id="todayBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                    Today
                </button>
            </div>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="grid grid-cols-7 bg-gray-50">
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Sun</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Mon</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Tue</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Wed</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Thu</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Fri</div>
            <div class="p-4 text-center text-sm font-medium text-gray-700">Sat</div>
        </div>
        <div id="calendarGrid" class="grid grid-cols-7">
            <!-- Calendar days will be generated by JavaScript -->
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// View Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const listViewBtn = document.getElementById('listViewBtn');
    const calendarViewBtn = document.getElementById('calendarViewBtn');
    const listView = document.getElementById('listView');
    const calendarView = document.getElementById('calendarView');

    // Switch to List View
    listViewBtn.addEventListener('click', function() {
        listView.classList.remove('hidden');
        calendarView.classList.add('hidden');

        // Update button styles
        listViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        listViewBtn.classList.remove('text-gray-600');
        calendarViewBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        calendarViewBtn.classList.add('text-gray-600');
    });

    // Switch to Calendar View
    calendarViewBtn.addEventListener('click', function() {
        listView.classList.add('hidden');
        calendarView.classList.remove('hidden');

        // Update button styles
        calendarViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        calendarViewBtn.classList.remove('text-gray-600');
        listViewBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        listViewBtn.classList.add('text-gray-600');

        // Initialize calendar
        initializeCalendar();
    });

    // Calendar functionality
    let currentDate = new Date();

    function initializeCalendar() {
        updateCalendarHeader();
        generateCalendar();
    }

    function updateCalendarHeader() {
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        document.getElementById('currentMonth').textContent =
            `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    }

    function generateCalendar() {
        const calendarGrid = document.getElementById('calendarGrid');
        calendarGrid.innerHTML = '';

        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Add empty cells for days before month starts
        for (let i = 0; i < startingDayOfWeek; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'h-32 border-r border-b border-gray-200 bg-gray-50';
            calendarGrid.appendChild(emptyDay);
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'h-32 border-r border-b border-gray-200 p-2 relative hover:bg-gray-50 cursor-pointer';

            const dayNumber = document.createElement('div');
            dayNumber.className = 'text-sm font-medium text-gray-900 mb-1';
            dayNumber.textContent = day;

            // Check if it's today
            const today = new Date();
            if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
                dayNumber.className += ' bg-indigo-600 text-white rounded-full w-6 h-6 flex items-center justify-center';
            }

            dayElement.appendChild(dayNumber);

            // Add sample sessions (you can replace this with real data)
            if (day % 3 === 0) {
                const session = document.createElement('div');
                session.className = 'text-xs bg-blue-100 text-blue-800 p-1 rounded mb-1 truncate';
                session.textContent = '10:00 - John D.';
                dayElement.appendChild(session);
            }

            if (day % 5 === 0) {
                const session = document.createElement('div');
                session.className = 'text-xs bg-green-100 text-green-800 p-1 rounded mb-1 truncate';
                session.textContent = '14:00 - Jane S.';
                dayElement.appendChild(session);
            }

            calendarGrid.appendChild(dayElement);
        }

        // Fill remaining cells
        const totalCells = calendarGrid.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        for (let i = 0; i < remainingCells; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'h-32 border-r border-b border-gray-200 bg-gray-50';
            calendarGrid.appendChild(emptyDay);
        }
    }

    // Calendar navigation
    document.getElementById('prevMonth').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        updateCalendarHeader();
        generateCalendar();
    });

    document.getElementById('nextMonth').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        updateCalendarHeader();
        generateCalendar();
    });

    document.getElementById('todayBtn').addEventListener('click', function() {
        currentDate = new Date();
        updateCalendarHeader();
        generateCalendar();
    });
});

// Session management functions
function markCompleted(sessionId) {
    if (confirm('Mark this session as completed?')) {
        // Handle mark as completed
        console.log('Marking session as completed:', sessionId);
    }
}

function markCancelled(sessionId) {
    if (confirm('Cancel this session?')) {
        // Handle cancellation
        console.log('Cancelling session:', sessionId);
    }
}

function confirmDelete(sessionId) {
    if (confirm('Are you sure you want to delete this session?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/gym/private-training/${sessionId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '<?php echo e(csrf_token()); ?>';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/private-training/index.blade.php ENDPATH**/ ?>