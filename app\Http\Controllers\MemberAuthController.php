<?php

namespace App\Http\Controllers;

use App\Models\Member;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class MemberAuthController extends Controller
{
    /**
     * Show the member login form.
     */
    public function showLoginForm(Request $request)
    {
        $module = $request->get('module', 'gym'); // Default to gym for backward compatibility
        return view('member.auth.login', compact('module'));
    }

    /**
     * Handle a member login request.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
            'remember' => 'boolean',
            'module' => 'required|string|in:gym,spa,restaurant,retail,events',
        ]);

        // Find member by email and check if active
        $member = Member::where('email', $request->email)
            ->where('is_active', true)
            ->first();

        if (!$member || !Hash::check($request->password, $member->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect or account is inactive.'],
            ]);
        }

        // Check if member has access to the requested module
        if (!$member->hasModuleAccess($request->module)) {
            throw ValidationException::withMessages([
                'email' => ['You do not have access to this module. Please contact support.'],
            ]);
        }

        // Log the member in using the member guard
        Auth::guard('member')->login($member, $request->filled('remember'));

        // Update last login time
        $member->update(['last_login_at' => now()]);

        // Store tenant and module info in session
        session([
            'member_tenant_id' => $member->tenant_id,
            'member_current_module' => $request->module,
        ]);

        // Regenerate session
        $request->session()->regenerate();

        return redirect()->intended($this->getMemberDashboardRoute($request->module));
    }

    /**
     * Show the member registration form.
     */
    public function showRegistrationForm(Request $request)
    {
        $module = $request->get('module', 'gym');
        return view('member.auth.register', compact('module'));
    }

    /**
     * Handle a member registration request.
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'module' => 'required|string|in:gym,spa,restaurant,retail,events',
            'member_type' => 'nullable|string|in:gym,spa,restaurant,retail,universal',
        ]);

        // Determine tenant ID from session, domain, or default
        $tenantId = $this->determineTenantId($request);

        // Check if email already exists for this tenant
        if (Member::where('tenant_id', $tenantId)->where('email', $request->email)->exists()) {
            throw ValidationException::withMessages([
                'email' => ['A member with this email already exists.'],
            ]);
        }

        // Generate member code
        $memberCode = Member::generateMemberCode($tenantId);

        // Determine member type
        $memberType = $request->member_type ?? $request->module;

        // Create the member with module access
        $member = Member::createWithModules([
            'tenant_id' => $tenantId,
            'member_code' => $memberCode,
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'date_of_birth' => $request->date_of_birth,
            'gender' => $request->gender,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'member_type' => $memberType,
            'is_active' => true,
        ], [$request->module]);

        // Log the member in
        Auth::guard('member')->login($member);

        // Store session data
        session([
            'member_tenant_id' => $member->tenant_id,
            'member_current_module' => $request->module,
        ]);

        return redirect()->route('member.dashboard', ['module' => $request->module])
            ->with('success', 'Registration successful! Welcome!');
    }

    /**
     * Log the member out.
     */
    public function logout(Request $request)
    {
        $currentModule = session('member_current_module', 'gym');
        
        Auth::guard('member')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('member.login', ['module' => $currentModule])
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show member dashboard.
     */
    public function dashboard(Request $request)
    {
        $module = $request->get('module', session('member_current_module', 'gym'));
        $member = Auth::guard('member')->user();

        if (!$member->hasModuleAccess($module)) {
            abort(403, 'You do not have access to this module.');
        }

        // Update session with current module
        session(['member_current_module' => $module]);

        return view('member.dashboard', compact('member', 'module'));
    }

    /**
     * Switch module for logged-in member.
     */
    public function switchModule(Request $request)
    {
        $request->validate([
            'module' => 'required|string|in:gym,spa,restaurant,retail,events',
        ]);

        $member = Auth::guard('member')->user();

        if (!$member->hasModuleAccess($request->module)) {
            return back()->with('error', 'You do not have access to this module.');
        }

        session(['member_current_module' => $request->module]);

        return redirect()->route('member.dashboard', ['module' => $request->module])
            ->with('success', 'Module switched successfully.');
    }

    /**
     * Get member dashboard route based on module.
     */
    protected function getMemberDashboardRoute(string $module): string
    {
        return match($module) {
            'gym' => route('member.dashboard', ['module' => 'gym']),
            'spa' => route('member.dashboard', ['module' => 'spa']),
            'restaurant' => route('member.dashboard', ['module' => 'restaurant']),
            'retail' => route('member.dashboard', ['module' => 'retail']),
            'events' => route('member.dashboard', ['module' => 'events']),
            default => route('member.dashboard', ['module' => 'gym'])
        };
    }

    /**
     * Determine tenant ID from various sources.
     */
    protected function determineTenantId(Request $request): int
    {
        // Try to get from session first
        if (session('member_tenant_id')) {
            return session('member_tenant_id');
        }

        // Try to get from authenticated user
        if (auth()->check()) {
            return auth()->user()->tenant_id;
        }

        // Try to get from subdomain or domain
        $host = $request->getHost();
        $subdomain = explode('.', $host)[0];
        
        $tenant = \App\Modules\Tenant\Models\Tenant::where('subdomain', $subdomain)->first();
        if ($tenant) {
            return $tenant->id;
        }

        // Default to first tenant (for development)
        return \App\Modules\Tenant\Models\Tenant::first()->id ?? 1;
    }
}
