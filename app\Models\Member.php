<?php

namespace App\Models;

use App\Modules\Tenant\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Member extends Authenticatable
{
    use HasFactory, SoftDeletes, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'members';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'member_code',
        'name',
        'email',
        'password',
        'phone',
        'date_of_birth',
        'gender',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'health_conditions',
        'notes',
        'photo',
        'qr_code',
        'is_active',
        'member_type', // New field to distinguish member types
        'modules', // JSON field to store which modules this member has access to
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'is_active' => 'boolean',
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password' => 'hashed',
        'modules' => 'array', // Cast modules as array
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the tenant that owns the member.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the gym-specific data for this member.
     */
    public function gymData(): HasMany
    {
        return $this->hasMany(\App\Modules\Gym\Models\GymMemberData::class, 'member_id');
    }

    /**
     * Get the subscriptions for the member (polymorphic).
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(\App\Models\MemberSubscription::class, 'member_id');
    }

    /**
     * Get gym subscriptions specifically.
     */
    public function gymSubscriptions(): HasMany
    {
        return $this->subscriptions()->where('module_type', 'gym');
    }

    /**
     * Get the attendances for the member (polymorphic).
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(\App\Models\MemberAttendance::class, 'member_id');
    }

    /**
     * Get gym attendances specifically.
     */
    public function gymAttendances(): HasMany
    {
        return $this->attendances()->where('module_type', 'gym');
    }

    /**
     * Check if member has access to a specific module.
     */
    public function hasModuleAccess(string $module): bool
    {
        return in_array($module, $this->modules ?? []);
    }

    /**
     * Add module access to member.
     */
    public function addModuleAccess(string $module): void
    {
        $modules = $this->modules ?? [];
        if (!in_array($module, $modules)) {
            $modules[] = $module;
            $this->update(['modules' => $modules]);
        }
    }

    /**
     * Remove module access from member.
     */
    public function removeModuleAccess(string $module): void
    {
        $modules = $this->modules ?? [];
        $modules = array_filter($modules, fn($m) => $m !== $module);
        $this->update(['modules' => array_values($modules)]);
    }

    /**
     * Get the active subscription for a specific module.
     */
    public function activeSubscriptionForModule(string $module)
    {
        return $this->subscriptions()
            ->where('module_type', $module)
            ->where('status', 'active')
            ->where('end_date', '>=', now())
            ->orderBy('end_date', 'desc')
            ->first();
    }

    /**
     * Check if the member has an active subscription for a module.
     */
    public function hasActiveSubscriptionForModule(string $module): bool
    {
        return $this->activeSubscriptionForModule($module) !== null;
    }

    /**
     * Get member type display name.
     */
    public function getMemberTypeDisplayAttribute(): string
    {
        return match($this->member_type) {
            'gym' => 'Gym Member',
            'spa' => 'Spa Member',
            'restaurant' => 'Restaurant Member',
            'retail' => 'Retail Customer',
            'universal' => 'Universal Member',
            default => 'Member'
        };
    }

    /**
     * Scope to filter by module access.
     */
    public function scopeWithModuleAccess($query, string $module)
    {
        return $query->whereJsonContains('modules', $module);
    }

    /**
     * Scope to filter by member type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('member_type', $type);
    }

    /**
     * Get available member types.
     */
    public static function getMemberTypes(): array
    {
        return [
            'gym' => 'Gym Member',
            'spa' => 'Spa Member',
            'restaurant' => 'Restaurant Member',
            'retail' => 'Retail Customer',
            'universal' => 'Universal Member',
        ];
    }

    /**
     * Get available modules.
     */
    public static function getAvailableModules(): array
    {
        return [
            'gym' => 'Gym & Fitness',
            'spa' => 'Spa & Wellness',
            'restaurant' => 'Restaurant & Dining',
            'retail' => 'Retail & Shopping',
            'events' => 'Events & Bookings',
        ];
    }

    /**
     * Generate unique member code.
     */
    public static function generateMemberCode(int $tenantId, string $prefix = 'MBR'): string
    {
        $count = static::where('tenant_id', $tenantId)->count() + 1;
        return $prefix . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create member with module access.
     */
    public static function createWithModules(array $data, array $modules = []): self
    {
        $data['modules'] = $modules;
        $member = static::create($data);

        return $member;
    }
}
