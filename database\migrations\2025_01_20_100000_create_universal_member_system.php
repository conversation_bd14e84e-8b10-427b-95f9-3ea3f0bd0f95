<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create universal members table
        if (!Schema::hasTable('members')) {
            Schema::create('members', function (Blueprint $table) {
                $table->id();
                $table->foreignId('tenant_id')->constrained('tenants')->cascadeOnDelete();
                $table->string('member_code')->unique();
                $table->string('name');
                $table->string('email')->nullable();
                $table->string('password')->nullable();
                $table->timestamp('email_verified_at')->nullable();
                $table->string('remember_token')->nullable();
                $table->timestamp('last_login_at')->nullable();
                $table->string('phone')->nullable();
                $table->date('date_of_birth')->nullable();
                $table->enum('gender', ['male', 'female', 'other'])->nullable();
                $table->text('address')->nullable();
                $table->string('emergency_contact_name')->nullable();
                $table->string('emergency_contact_phone')->nullable();
                $table->text('health_conditions')->nullable();
                $table->text('notes')->nullable();
                $table->string('photo')->nullable();
                $table->string('qr_code')->unique()->nullable();
                $table->enum('member_type', ['gym', 'spa', 'restaurant', 'retail', 'universal'])->default('gym');
                $table->json('modules')->nullable(); // Which modules this member has access to
                $table->boolean('is_active')->default(true);
                $table->timestamps();
                $table->softDeletes();

                // Indexes
                $table->index(['tenant_id', 'email']);
                $table->index(['tenant_id', 'member_code']);
                $table->index(['tenant_id', 'is_active']);
                $table->index(['tenant_id', 'member_type']);
            });
        }

        // Create universal member subscriptions table
        if (!Schema::hasTable('member_subscriptions')) {
            Schema::create('member_subscriptions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('tenant_id')->constrained('tenants')->cascadeOnDelete();
                $table->foreignId('member_id')->constrained('members')->cascadeOnDelete();
                $table->morphs('subscribable'); // Polymorphic relation to different membership types
                $table->enum('module_type', ['gym', 'spa', 'restaurant', 'retail', 'events'])->default('gym');
                $table->string('subscription_code')->unique();
                $table->date('start_date');
                $table->date('end_date');
                $table->enum('status', ['active', 'expired', 'cancelled', 'suspended', 'pending'])->default('active');
                $table->decimal('price', 10, 2);
                $table->string('payment_method')->nullable();
                $table->enum('payment_status', ['pending', 'paid', 'partial', 'failed'])->default('pending');
                $table->boolean('auto_renewal')->default(false);
                $table->date('renewal_date')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
                $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
                $table->timestamps();

                // Indexes
                $table->index(['tenant_id', 'member_id']);
                $table->index(['tenant_id', 'module_type']);
                $table->index(['tenant_id', 'status']);
                $table->index(['start_date', 'end_date']);
            });
        }

        // Create universal member attendance table
        if (!Schema::hasTable('member_attendances')) {
            Schema::create('member_attendances', function (Blueprint $table) {
                $table->id();
                $table->foreignId('tenant_id')->constrained('tenants')->cascadeOnDelete();
                $table->foreignId('member_id')->constrained('members')->cascadeOnDelete();
                $table->enum('module_type', ['gym', 'spa', 'restaurant', 'retail', 'events'])->default('gym');
                $table->morphs('attendable'); // Polymorphic relation to different entities (gym sessions, spa appointments, etc.)
                $table->datetime('check_in_time');
                $table->datetime('check_out_time')->nullable();
                $table->enum('status', ['checked_in', 'checked_out', 'no_show'])->default('checked_in');
                $table->text('notes')->nullable();
                $table->timestamps();

                // Indexes
                $table->index(['tenant_id', 'member_id']);
                $table->index(['tenant_id', 'module_type']);
                $table->index(['check_in_time']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('member_attendances');
        Schema::dropIfExists('member_subscriptions');
        Schema::dropIfExists('members');
    }
};
