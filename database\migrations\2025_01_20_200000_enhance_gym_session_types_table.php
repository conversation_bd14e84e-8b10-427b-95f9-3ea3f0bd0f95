<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gym_session_types', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('gym_session_types', 'slug')) {
                $table->string('slug')->nullable()->after('name');
            }
            if (!Schema::hasColumn('gym_session_types', 'category')) {
                $table->enum('category', ['fitness', 'nutrition', 'wellness', 'rehabilitation', 'assessment', 'specialized'])
                      ->default('fitness')->after('description');
            }
            if (!Schema::hasColumn('gym_session_types', 'default_price')) {
                $table->decimal('default_price', 10, 2)->nullable()->after('duration_minutes');
            }
            if (!Schema::hasColumn('gym_session_types', 'features')) {
                $table->json('features')->nullable()->after('icon');
            }
            if (!Schema::hasColumn('gym_session_types', 'requirements')) {
                $table->json('requirements')->nullable()->after('features');
            }
            if (!Schema::hasColumn('gym_session_types', 'benefits')) {
                $table->json('benefits')->nullable()->after('requirements');
            }
            if (!Schema::hasColumn('gym_session_types', 'target_audience')) {
                $table->json('target_audience')->nullable()->after('benefits');
            }
            if (!Schema::hasColumn('gym_session_types', 'equipment_needed')) {
                $table->json('equipment_needed')->nullable()->after('target_audience');
            }
            if (!Schema::hasColumn('gym_session_types', 'preparation_notes')) {
                $table->text('preparation_notes')->nullable()->after('equipment_needed');
            }
            if (!Schema::hasColumn('gym_session_types', 'post_session_notes')) {
                $table->text('post_session_notes')->nullable()->after('preparation_notes');
            }
            if (!Schema::hasColumn('gym_session_types', 'is_popular')) {
                $table->boolean('is_popular')->default(false)->after('is_active');
            }
        });

        // Update existing records to have slugs
        $sessionTypes = \DB::table('gym_session_types')->whereNull('slug')->orWhere('slug', '')->get();
        foreach ($sessionTypes as $sessionType) {
            $slug = \Illuminate\Support\Str::slug($sessionType->name);
            if (empty($slug)) {
                $slug = 'session-type-' . $sessionType->id;
            }

            // Ensure uniqueness
            $originalSlug = $slug;
            $counter = 1;
            while (\DB::table('gym_session_types')->where('slug', $slug)->where('id', '!=', $sessionType->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            \DB::table('gym_session_types')->where('id', $sessionType->id)->update(['slug' => $slug]);
        }

        // Now make slug unique
        Schema::table('gym_session_types', function (Blueprint $table) {
            $table->string('slug')->unique()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gym_session_types', function (Blueprint $table) {
            $table->dropColumn([
                'slug',
                'category',
                'default_price',
                'features',
                'requirements',
                'benefits',
                'target_audience',
                'equipment_needed',
                'preparation_notes',
                'post_session_notes',
                'is_popular'
            ]);
        });
    }
};
