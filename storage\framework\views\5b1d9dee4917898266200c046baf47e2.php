<?php $__env->startSection('page-title', 'Attendance Records'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Attendance Records</h2>
        <p class="text-gray-600">Track member check-ins and check-outs</p>
    </div>
    <a href="<?php echo e(route('gym.attendance.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Record Attendance
    </a>
</div>

<!-- Search and Filters -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
        <i class="fas fa-filter mr-2 text-indigo-600"></i>
        Filter Attendance
    </h3>
    <form action="<?php echo e(route('gym.attendance.index')); ?>" method="GET" class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search by member name or code"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div class="w-full md:w-48">
                            <select name="member_id" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Members</option>
                                <?php $__currentLoopData = $members; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($member->id); ?>" <?php echo e(request('member_id') == $member->id ? 'selected' : ''); ?>>
                                        <?php echo e($member->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="w-full md:w-48">
                            <input type="date" name="date_from" value="<?php echo e(request('date_from')); ?>" placeholder="Date From"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div class="w-full md:w-48">
                            <input type="date" name="date_to" value="<?php echo e(request('date_to')); ?>" placeholder="Date To"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
        <div>
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200">
                <i class="fas fa-search mr-2"></i>
                Filter
            </button>
        </div>
    </form>
</div>

<!-- Attendance Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check In</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check Out</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php $__empty_1 = true; $__currentLoopData = $attendances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="py-4 px-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <?php if($attendance->member->photo): ?>
                                                    <img class="h-10 w-10 rounded-full object-cover" src="<?php echo e(Storage::url($attendance->member->photo)); ?>" alt="<?php echo e($attendance->member->name); ?>">
                                                <?php else: ?>
                                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <span class="text-gray-600"><?php echo e(substr($attendance->member->name, 0, 1)); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo e($attendance->member->name); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo e($attendance->member->member_code); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 px-4">
                                        <div class="text-sm text-gray-900"><?php echo e($attendance->date->format('D, M d, Y')); ?></div>
                                    </td>
                                    <td class="py-4 px-4">
                                        <div class="text-sm text-gray-900"><?php echo e(substr($attendance->check_in_time, 0, 5)); ?></div>
                                    </td>
                                    <td class="py-4 px-4">
                                        <?php if($attendance->check_out_time): ?>
                                            <div class="text-sm text-gray-900"><?php echo e(substr($attendance->check_out_time, 0, 5)); ?></div>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Not checked out
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-4 px-4">
                                        <div class="text-sm text-gray-900">
                                            <?php if($attendance->check_out_time): ?>
                                                <?php echo e($attendance->getFormattedDuration()); ?>

                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="py-4 px-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="<?php echo e(route('gym.attendance.show', $attendance)); ?>" class="text-indigo-600 hover:text-indigo-900"><i class="fas fa-eye mr-2"></i>View</a>
                                            <a href="<?php echo e(route('gym.attendance.edit', $attendance)); ?>" class="text-yellow-600 hover:text-yellow-900"><i class="fas fa-edit mr-2"></i>Edit</a>
                                            <form action="<?php echo e(route('gym.attendance.destroy', $attendance)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this attendance record?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 hover:text-red-900"><i class="fas fa-trash mr-2"></i>Delete</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="py-4 px-4 text-center text-gray-500">
                                        No attendance records found.
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        <?php echo e($attendances->withQueryString()->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/attendance/index.blade.php ENDPATH**/ ?>