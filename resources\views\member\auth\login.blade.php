<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Login - {{ ucfirst($module ?? 'gym') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                @php
                    $moduleConfig = [
                        'gym' => ['icon' => 'fa-dumbbell', 'color' => 'from-blue-500 to-blue-600', 'name' => 'Gym & Fitness'],
                        'spa' => ['icon' => 'fa-spa', 'color' => 'from-purple-500 to-purple-600', 'name' => 'Spa & Wellness'],
                        'restaurant' => ['icon' => 'fa-utensils', 'color' => 'from-green-500 to-green-600', 'name' => 'Restaurant & Dining'],
                        'retail' => ['icon' => 'fa-shopping-bag', 'color' => 'from-yellow-500 to-yellow-600', 'name' => 'Retail & Shopping'],
                        'events' => ['icon' => 'fa-calendar-alt', 'color' => 'from-red-500 to-red-600', 'name' => 'Events & Bookings'],
                    ];
                    $config = $moduleConfig[$module ?? 'gym'] ?? $moduleConfig['gym'];
                @endphp
                
                <div class="mx-auto h-20 w-20 bg-gradient-to-r {{ $config['color'] }} rounded-full flex items-center justify-center mb-6">
                    <i class="fas {{ $config['icon'] }} text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900">{{ $config['name'] }}</h2>
                <p class="mt-2 text-sm text-gray-600">Sign in to your member account</p>
            </div>

            <!-- Login Form -->
            <div class="bg-white rounded-2xl shadow-xl p-8">
                @if ($errors->any())
                    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                @if (session('success'))
                    <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                            <p class="text-sm text-green-700">{{ session('success') }}</p>
                        </div>
                    </div>
                @endif

                <form method="POST" action="{{ route('member.login') }}" class="space-y-6">
                    @csrf
                    <input type="hidden" name="module" value="{{ $module ?? 'gym' }}">

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Enter your email">
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" name="password" id="password" required
                                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Enter your password">
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input type="checkbox" name="remember" id="remember" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                        </div>
                        <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500">Forgot password?</a>
                    </div>

                    <button type="submit" class="w-full bg-gradient-to-r {{ $config['color'] }} text-white py-2 px-4 rounded-lg font-medium hover:opacity-90 transition-opacity">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign In
                    </button>
                </form>

                <!-- Register Link -->
                <div class="text-center pt-6 border-t border-gray-200 mt-6">
                    <p class="text-sm text-gray-600">
                        Don't have an account? 
                        <a href="{{ route('member.register', ['module' => $module ?? 'gym']) }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                            Register here
                        </a>
                    </p>
                </div>

                <!-- Back to Module Selection -->
                <div class="text-center pt-4">
                    <a href="{{ route('member.welcome') }}" class="text-sm text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to module selection
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
