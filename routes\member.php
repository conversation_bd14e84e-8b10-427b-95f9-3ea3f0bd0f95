<?php

use App\Http\Controllers\MemberAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Universal Member Routes
|--------------------------------------------------------------------------
|
| These routes handle member authentication and dashboard access
| for all modules (gym, spa, restaurant, retail, events).
|
*/

// Member Authentication Routes (Public Access)
Route::middleware(['web'])->prefix('member')->name('member.')->group(function () {

    // Welcome page (accessible to all)
    Route::get('/', function () {
        return view('member.welcome');
    })->name('welcome');

    // Session types page (accessible to all)
    Route::get('/session-types', function () {
        return view('member.session-types');
    })->name('session-types');

    // Guest routes (accessible only when not logged in as member)
    Route::middleware(['guest:member'])->group(function () {
        Route::get('login', [MemberAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [MemberAuthController::class, 'login']);
        Route::get('register', [MemberAuthController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [MemberAuthController::class, 'register']);
    });

    // Authenticated member routes
    Route::middleware(['auth:member'])->group(function () {
        Route::post('logout', [MemberAuthController::class, 'logout'])->name('logout');
        Route::get('dashboard', [MemberAuthController::class, 'dashboard'])->name('dashboard');
        Route::post('switch-module', [MemberAuthController::class, 'switchModule'])->name('switch-module');

        // Profile management
        Route::get('profile', [MemberAuthController::class, 'profile'])->name('profile');
        Route::put('profile', [MemberAuthController::class, 'updateProfile'])->name('profile.update');
        Route::put('password', [MemberAuthController::class, 'updatePassword'])->name('password.update');
    });
});

// Backward compatibility routes for gym module
Route::middleware(['web'])->prefix('gym/member')->name('gym.member.')->group(function () {

    // Redirect old gym member routes to new universal routes
    Route::get('login', function () {
        return redirect()->route('member.login', ['module' => 'gym']);
    })->name('login');

    Route::get('register', function () {
        return redirect()->route('member.register', ['module' => 'gym']);
    })->name('register');

    Route::get('dashboard', function () {
        return redirect()->route('member.dashboard', ['module' => 'gym']);
    })->name('dashboard');
});
