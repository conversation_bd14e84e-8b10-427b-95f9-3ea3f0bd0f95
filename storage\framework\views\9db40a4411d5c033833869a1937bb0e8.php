<?php $__env->startSection('page-title', 'Private Training Packages'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h2 class="text-2xl font-semibold text-gray-800">Private Training Packages</h2>
        <p class="text-gray-600">Manage private training service packages and pricing</p>
    </div>
    <a href="<?php echo e(route('gym.private-training.packages.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
        <i class="fas fa-plus mr-2"></i>
        Create Package
    </a>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-500 text-white mr-4">
                <i class="fas fa-box"></i>
            </div>
            <div>
                <p class="text-sm text-blue-800 font-medium">Total Packages</p>
                <p class="text-2xl font-bold text-blue-900"><?php echo e($packages->total() ?? 0); ?></p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-500 text-white mr-4">
                <i class="fas fa-check-circle"></i>
            </div>
            <div>
                <p class="text-sm text-green-800 font-medium">Active Packages</p>
                <p class="text-2xl font-bold text-green-900"><?php echo e($packages->where('is_active', true)->count() ?? 0); ?></p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-500 text-white mr-4">
                <i class="fas fa-users"></i>
            </div>
            <div>
                <p class="text-sm text-purple-800 font-medium">Active Clients</p>
                <p class="text-2xl font-bold text-purple-900">45</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-500 text-white mr-4">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div>
                <p class="text-sm text-yellow-800 font-medium">Monthly Revenue</p>
                <p class="text-2xl font-bold text-yellow-900">Rp 25M</p>
            </div>
        </div>
    </div>
</div>

<!-- Packages Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <?php $__empty_1 = true; $__currentLoopData = $packages ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="bg-white rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow">
            <!-- Package Header -->
            <div class="bg-white border-b border-gray-200 p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900"><?php echo e($package->name ?? 'Personal Training Package'); ?></h3>
                        <p class="text-gray-600 mt-1"><?php echo e($package->description ?? 'Professional personal training sessions'); ?></p>
                    </div>
                    <?php if($package->is_popular ?? false): ?>
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-bold px-2 py-1 rounded-full">POPULAR</span>
                    <?php endif; ?>
                </div>

                <!-- Price -->
                <div class="mt-4">
                    <div class="flex items-baseline">
                        <span class="text-3xl font-bold text-gray-900">Rp <?php echo e(number_format($package->price ?? 150000, 0, ',', '.')); ?></span>
                        <span class="text-gray-600 ml-2">/ <?php echo e($package->sessions_count ?? 1); ?> session<?php echo e(($package->sessions_count ?? 1) > 1 ? 's' : ''); ?></span>
                    </div>
                    <?php if(($package->sessions_count ?? 1) > 1): ?>
                        <p class="text-gray-600 text-sm mt-1">
                            Rp <?php echo e(number_format(($package->price ?? 150000) / ($package->sessions_count ?? 1), 0, ',', '.')); ?> per session
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Package Features -->
            <div class="p-6">
                <ul class="space-y-3 mb-6">
                    <?php if(isset($package->features) && is_array($package->features)): ?>
                        <?php $__currentLoopData = $package->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-700"><?php echo e($feature); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700"><?php echo e($package->sessions_count ?? 1); ?> personal training session<?php echo e(($package->sessions_count ?? 1) > 1 ? 's' : ''); ?></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Customized workout plan</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Progress tracking</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Nutrition guidance</span>
                        </li>
                        <?php if(($package->sessions_count ?? 1) >= 5): ?>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-700">Free fitness assessment</span>
                            </li>
                        <?php endif; ?>
                        <?php if(($package->sessions_count ?? 1) >= 10): ?>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-700">Monthly body composition analysis</span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>

                <!-- Package Stats -->
                <div class="border-t border-gray-200 pt-4 mb-4">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold text-gray-900"><?php echo e($package->active_subscriptions_count ?? 0); ?></p>
                            <p class="text-sm text-gray-500">Active Clients</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900"><?php echo e($package->validity_days ?? 90); ?></p>
                            <p class="text-sm text-gray-500">Days Validity</p>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <?php if($package->is_active ?? true): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                        </span>
                    <?php else: ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                        </span>
                    <?php endif; ?>

                    <?php if($package->trainer_commission ?? false): ?>
                        <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            <?php echo e($package->trainer_commission); ?>% Commission
                        </span>
                    <?php endif; ?>
                </div>

                <!-- Actions -->
                <div class="flex space-x-2">
                    <a href="<?php echo e(route('gym.private-training.packages.show', $package)); ?>" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 text-center py-2 px-4 rounded-lg text-sm">
                        <i class="fas fa-eye mr-1"></i>View
                    </a>
                    <a href="<?php echo e(route('gym.private-training.packages.edit', $package)); ?>" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-4 rounded-lg text-sm">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </a>
                    <form action="<?php echo e(route('gym.private-training.packages.destroy', $package)); ?>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this package?');">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded-lg text-sm">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-span-full">
            <div class="bg-white rounded-lg shadow p-12 text-center">
                <i class="fas fa-box text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-medium text-gray-900 mb-2">No packages found</h3>
                <p class="text-gray-500 mb-6">Create your first private training package</p>
                <a href="/gym/private-training/packages/create" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>Create First Package
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Default Packages (if no packages exist) -->
<?php if(($packages ?? collect())->isEmpty()): ?>
    <div class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Suggested Packages</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Single Session -->
            <div class="bg-white rounded-lg shadow overflow-hidden border-2 border-dashed border-blue-300 hover:border-blue-500 transition-colors">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 suggested-package-header">
                    <h3 class="text-xl font-bold text-white" style="color: white !important;">Single Session</h3>
                    <p class="text-blue-100 mt-1">Perfect for trying out personal training</p>
                    <div class="mt-4">
                        <span class="text-3xl font-bold text-white price-text" style="color: white !important;">Rp 150,000</span>
                        <span class="text-blue-100 ml-2">/ 1 session</span>
                    </div>
                </div>
                <div class="p-6 bg-white">
                    <ul class="space-y-2 mb-4">
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">1 personal training session</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Basic fitness assessment</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Workout plan guidance</span>
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                        Create This Package
                    </button>
                </div>
            </div>

            <!-- 5 Sessions Package -->
            <div class="bg-white rounded-lg shadow overflow-hidden border-2 border-dashed border-green-300 hover:border-green-500 transition-colors">
                <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 suggested-package-header">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-xl font-bold text-white" style="color: white !important;">5 Sessions Package</h3>
                            <p class="text-green-100 mt-1">Most popular choice</p>
                        </div>
                        <span class="bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded">POPULAR</span>
                    </div>
                    <div class="mt-4">
                        <span class="text-3xl font-bold text-white price-text" style="color: white !important;">Rp 700,000</span>
                        <span class="text-green-100 ml-2">/ 5 sessions</span>
                        <p class="text-green-100 text-sm mt-1">Rp 140,000 per session</p>
                    </div>
                </div>
                <div class="p-6 bg-white">
                    <ul class="space-y-2 mb-4">
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">5 personal training sessions</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Comprehensive fitness assessment</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Customized workout plan</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Nutrition guidance</span>
                        </li>
                    </ul>
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                        Create This Package
                    </button>
                </div>
            </div>

            <!-- 10 Sessions Package -->
            <div class="bg-white rounded-lg shadow overflow-hidden border-2 border-dashed border-purple-300 hover:border-purple-500 transition-colors">
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 suggested-package-header">
                    <h3 class="text-xl font-bold text-white" style="color: white !important;">10 Sessions Package</h3>
                    <p class="text-purple-100 mt-1">Best value for serious training</p>
                    <div class="mt-4">
                        <span class="text-3xl font-bold text-white price-text" style="color: white !important;">Rp 1,300,000</span>
                        <span class="text-purple-100 ml-2">/ 10 sessions</span>
                        <p class="text-purple-100 text-sm mt-1">Rp 130,000 per session</p>
                    </div>
                </div>
                <div class="p-6 bg-white">
                    <ul class="space-y-2 mb-4">
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">10 personal training sessions</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Complete fitness assessment</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Personalized workout & nutrition plan</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Monthly body composition analysis</span>
                        </li>
                        <li class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span class="text-gray-700">Progress tracking & adjustments</span>
                        </li>
                    </ul>
                    <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                        Create This Package
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Force white text color for suggested package headers */
.suggested-package-header h3 {
    color: white !important;
}
.suggested-package-header .price-text {
    color: white !important;
}
.suggested-package-header span {
    color: inherit !important;
}
</style>
<?php $__env->stopPush(); ?>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('gym::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\app\Modules/Gym/Resources/views/private-training/packages.blade.php ENDPATH**/ ?>